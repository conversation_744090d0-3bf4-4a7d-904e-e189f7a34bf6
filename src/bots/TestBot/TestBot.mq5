//+------------------------------------------------------------------+
//| TestBot.mq5 - บอททดสอบสำหรับการทดสอบระบบ
//+------------------------------------------------------------------+
#property copyright "TradingBots Team"
#property link      "https://github.com/tradingbots"
#property version   "1.00"
#property description "บอททดสอบสำหรับการทดสอบระบบ"

//--- Include files
#include "../../core/includes/Constants.mqh"
#include "../../core/includes/OrderManager.mqh"
#include "../../core/includes/IndicatorManager.mqh"
#include "../../core/classes/PerformanceAnalyzer.mqh"

//--- ค่าคงที่จาก Constants.mqh (ไม่ใช่ input parameters)
// ค่าเหล่านี้จะถูกใช้โดยตรงจาก Constants.mqh
// ไม่สามารถปรับเปลี่ยนได้ใน Strategy Tester

input group "=== Trading Settings ==="
input bool EnableTrading = true;          // เปิดใช้งานการเทรด
input int MagicNumber = 123456;           // Magic Number
input string TradeComment = "TestBot"; // ข้อความใน Order

//--- Global Variables
COrderManager orderManager;               // ตัวจัดการ Order
CIndicatorManager indicatorManager;       // ตัวจัดการ Indicators
CPerformanceAnalyzer performanceAnalyzer; // ตัววิเคราะห์ประสิทธิภาพ

datetime lastBarTime;                     // เวลาของแท่งเทียนล่าสุด
bool isNewBar;                           // ตรวจสอบแท่งเทียนใหม่

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== TestBot Initialization ===");
    
    // เริ่มต้น Order Manager
    orderManager.SetSymbol(Symbol());
    
    // ใช้ค่าคงที่จาก Constants.mqh แทน _Point เพื่อความแม่นยำ
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if(Symbol() == "XAUUSD.")
    {
        point = 0.1; // 1 pip = 0.1 สำหรับ XAUUSD
    }
    
    // ใช้ค่าจาก Constants.mqh โดยตรง
    orderManager.SetStopLoss(DEFAULT_STOP_LOSS_PIPS * point);
    orderManager.SetTakeProfit(DEFAULT_TAKE_PROFIT_PIPS * point);
    
    // คำนวณขนาด Lot ตามความเสี่ยง
    double lotSize = orderManager.CalculateLotSize(DEFAULT_RISK_PERCENT, DEFAULT_STOP_LOSS_PIPS);
    orderManager.SetLotSize(lotSize);
    
    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);
    Print("Stop Loss: ", DEFAULT_STOP_LOSS_PIPS, " pips (", DEFAULT_STOP_LOSS_PIPS * point, " ราคา)");
    Print("Take Profit: ", DEFAULT_TAKE_PROFIT_PIPS, " pips (", DEFAULT_TAKE_PROFIT_PIPS * point, " ราคา)");
    Print("Point ที่ใช้: ", point);
    
    // เริ่มต้น Indicator Manager
    if(!indicatorManager.Initialize())
    {
        Print("ข้อผิดพลาด: ไม่สามารถเริ่มต้น Indicators ได้");
        return INIT_FAILED;
    }
    
    // เริ่มต้น Performance Analyzer
    performanceAnalyzer.Initialize();
    
    Print("Indicators เริ่มต้นสำเร็จ");
    
    // ตั้งค่าเวลาเริ่มต้น
    lastBarTime = iTime(Symbol(), Period(), 0);
    
    Print("=== TestBot พร้อมใช้งาน ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== TestBot Deinitialization ===");
    
    // ปิด Indicators
    indicatorManager.Deinitialize();
    
    // ปิด Order ทั้งหมด
    orderManager.CloseAllOrders();
    
    // บันทึกสถิติประสิทธิภาพ
    performanceAnalyzer.SaveStatistics();
    
    Print("TestBot ถูกปิดแล้ว");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // ตรวจสอบแท่งเทียนใหม่
    if(!IsNewBar())
        return;
    
    // ตรวจสอบการเปิดใช้งานการเทรด
    if(!EnableTrading)
        return;
    
    // ตรวจสอบ Order ที่เปิดอยู่
    if(orderManager.HasOpenOrders())
        return;
    
    // วิเคราะห์สัญญาณการเทรด
    int signal = AnalyzeTradingSignal();
    
    // ดำเนินการตามสัญญาณ
    if(signal == 1) // สัญญาณ Buy
    {
        if(orderManager.OpenBuyOrder())
        {
            Print("เปิด Order Buy สำเร็จ");
            performanceAnalyzer.RecordTrade(ORDER_TYPE_BUY, Ask, DEFAULT_STOP_LOSS_PIPS, DEFAULT_TAKE_PROFIT_PIPS);
        }
    }
    else if(signal == -1) // สัญญาณ Sell
    {
        if(orderManager.OpenSellOrder())
        {
            Print("เปิด Order Sell สำเร็จ");
            performanceAnalyzer.RecordTrade(ORDER_TYPE_SELL, Bid, DEFAULT_STOP_LOSS_PIPS, DEFAULT_TAKE_PROFIT_PIPS);
        }
    }
}

//+------------------------------------------------------------------+
//| ตรวจสอบแท่งเทียนใหม่                                            |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        isNewBar = true;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| วิเคราะห์สัญญาณการเทรด                                          |
//+------------------------------------------------------------------+
int AnalyzeTradingSignal()
{
    // TODO: เพิ่มตรรกะการวิเคราะห์สัญญาณตามกลยุทธ์ที่ต้องการ
    
    // ตัวอย่าง: ใช้ RSI และ MACD
    double rsi = indicatorManager.GetRSI(14, 1);
    double macd = indicatorManager.GetMACD(12, 26, 9, 1);
    double macdSignal = indicatorManager.GetMACDSignal(12, 26, 9, 1);
    
    // สัญญาณ Buy: RSI oversold + MACD bullish
    if(rsi < 30 && macd > macdSignal)
    {
        return 1; // สัญญาณ Buy
    }
    
    // สัญญาณ Sell: RSI overbought + MACD bearish
    if(rsi > 70 && macd < macdSignal)
    {
        return -1; // สัญญาณ Sell
    }
    
    return 0; // ไม่มีสัญญาณ
}
