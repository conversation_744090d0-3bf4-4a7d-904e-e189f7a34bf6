# คู่มือการติดตั้งและใช้งาน MyFirstBot

## ข้อกำหนดเบื้องต้น

### ระบบที่ต้องการ

- **MetaTrader 5** เวอร์ชันล่าสุด
- **Account Demo** หรือ Live Account
- **โบรกเกอร์** ที่รองรับ XAUUSD
- **ความรู้พื้นฐาน** ในการใช้งาน MT5

### โบรกเกอร์ที่แนะนำ

- **iux** (ที่คุณใช้อยู่)
- **IC Markets**
- **Pepperstone**
- **FXTM**

## ขั้นตอนการติดตั้ง

### ขั้นตอนที่ 1: ดาวน์โหลดไฟล์

1. คัดลอกไฟล์ทั้งหมดจากโปรเจคนี้
2. เก็บไว้ในโฟลเดอร์ที่ปลอดภัย

### ขั้นตอนที่ 2: ติดตั้งใน MT5

1. เปิด MetaTrader 5
2. ไปที่เมนู **File** → **Open Data Folder**
3. เปิดโฟลเดอร์ **MQL5**
4. เปิดโฟลเดอร์ **Experts**
5. คัดลอกไฟล์ `src/main.mq5` ไปยังโฟลเดอร์ **Experts**
6. รีสตาร์ท MetaTrader 5

### ขั้นตอนที่ 3: ตรวจสอบการติดตั้ง

1. ไปที่ **Navigator** (กด Ctrl+N)
2. เปิดโฟลเดอร์ **Expert Advisors**
3. ควรเห็น **MyFirstBot** ในรายการ
4. หากไม่เห็น ให้รีสตาร์ท MT5 อีกครั้ง

## การตั้งค่าเริ่มต้น

### การเปิดชาร์ต XAUUSD

1. ไปที่ **Market Watch** (กด Ctrl+M)
2. ค้นหา **XAUUSD**
3. คลิกขวาที่ **XAUUSD** → **Chart Window**
4. เลือก Timeframe ที่ต้องการ (แนะนำ M15 หรือ H1)

### การตั้งค่า EA

1. ลาก **MyFirstBot** จาก Navigator ไปยังชาร์ต
2. ตั้งค่าพารามิเตอร์ตามต้องการ:

#### Risk Management

```
RiskPercent: 2.0        // ความเสี่ยง 2% ต่อการเทรด
StopLossPips: 50        // Stop Loss 50 pips
TakeProfitPips: 100     // Take Profit 100 pips
```

#### Indicator Settings

```
RSIPeriod: 14           // คาบเวลาของ RSI
MACDFastEMA: 12         // Fast EMA สำหรับ MACD
MACDSlowEMA: 26         // Slow EMA สำหรับ MACD
MACDSignalEMA: 9        // Signal EMA สำหรับ MACD
BollingerPeriod: 20     // คาบเวลาของ Bollinger Bands
BollingerDeviation: 2.0 // ค่า Standard Deviation
```

#### Trading Settings

```
EnableTrading: true     // เปิดใช้งานการเทรด
MagicNumber: 123456     // Magic Number
TradeComment: MyFirstBot // ข้อความใน Order
```

3. กดปุ่ม **OK**

## การใช้งาน

### การเริ่มต้นการทำงาน

1. ตรวจสอบว่า **AutoTrading** เปิดใช้งาน (ปุ่มสีเขียว)
2. EA จะเริ่มทำงานทันที
3. ตรวจสอบ **Journal** tab สำหรับข้อความเริ่มต้น

### การตรวจสอบสถานะ

- **Comment บนชาร์ต**: แสดงสถานะปัจจุบัน
- **Journal tab**: แสดงข้อความและข้อผิดพลาด
- **Expert tab**: แสดงข้อมูลการทำงานของ EA

### การหยุดการทำงาน

1. คลิกขวาที่ EA บนชาร์ต
2. เลือก **Remove** หรือ **Properties**
3. หรือตั้งค่า **EnableTrading = false**

## การทดสอบ

### Backtest

1. ไปที่ **View** → **Strategy Tester** (กด Ctrl+R)
2. เลือก **Expert**: MyFirstBot
3. เลือก **Symbol**: XAUUSD
4. เลือก **Period**: M15 หรือ H1
5. ตั้งค่าวันที่เริ่มต้นและสิ้นสุด
6. กดปุ่ม **Start**

### Forward Test

1. ใช้กับ **Account Demo** ก่อน
2. เปิดใช้งาน EA บนชาร์ตจริง
3. ตรวจสอบการทำงานในสภาพแวดล้อมจริง
4. ปรับแต่งพารามิเตอร์ตามผลลัพธ์

## การปรับแต่ง

### การปรับ Risk Management

- **ลด RiskPercent**: หากต้องการความเสี่ยงต่ำ
- **เพิ่ม StopLossPips**: หากต้องการให้ Order อยู่ได้นานขึ้น
- **ปรับ TakeProfitPips**: ตามเป้าหมายกำไร

### การปรับ Indicators

- **RSI**: ปรับคาบเวลาตามความต้องการ
- **MACD**: เปลี่ยนค่า EMA ตามความเร็วของตลาด
- **Bollinger**: ปรับ Deviation ตามความผันผวน

### การปรับเวลาการเทรด

- เปลี่ยน **TRADING_START_HOUR** และ **TRADING_END_HOUR**
- ปรับวันทำงานตามต้องการ

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### EA ไม่ทำงาน

**สาเหตุ**: AutoTrading ปิด หรือการตั้งค่าไม่ถูกต้อง
**วิธีแก้**:

1. ตรวจสอบปุ่ม AutoTrading (สีเขียว)
2. ตรวจสอบการตั้งค่า EA
3. ดูข้อความใน Journal tab

#### ไม่เปิด Order

**สาเหตุ**: ไม่มีสัญญาณจาก Indicators หรือเงื่อนไขไม่ตรง
**วิธีแก้**:

1. ตรวจสอบสัญญาณจาก Indicators
2. ตรวจสอบ Spread
3. ตรวจสอบเวลาการเทรด

#### Order ถูกปิดทันที

**สาเหตุ**: Stop Loss หรือ Take Profit ไม่ถูกต้อง
**วิธีแก้**:

1. ตรวจสอบการตั้งค่า Stop Loss และ Take Profit
2. ตรวจสอบขนาด Lot
3. ตรวจสอบ Margin

#### ข้อผิดพลาดในการคอมไพล์

**สาเหตุ**: โค้ดมีข้อผิดพลาดหรือ library ไม่ครบ
**วิธีแก้**:

1. ตรวจสอบข้อผิดพลาดใน MetaEditor
2. ตรวจสอบการ include files
3. ตรวจสอบ syntax

### การติดต่อขอความช่วยเหลือ

หากมีปัญหาที่แก้ไขไม่ได้:

1. ตรวจสอบ **Journal** และ **Expert** tabs
2. บันทึกข้อผิดพลาดและข้อความ
3. ตรวจสอบคู่มือนี้และเอกสารอื่นๆ
4. ตรวจสอบการตั้งค่าทั้งหมด

## การบำรุงรักษา

### การตรวจสอบประจำ

- ตรวจสอบการทำงานของ EA ทุกวัน
- ตรวจสอบผลการเทรดและสถิติ
- ตรวจสอบการตั้งค่าและพารามิเตอร์

### การอัพเดท

- อัพเดท MT5 เป็นเวอร์ชันล่าสุด
- อัพเดทพารามิเตอร์ตามสภาพตลาด
- สำรองข้อมูลการตั้งค่า

### การสำรองข้อมูล

- สำรองไฟล์ EA
- สำรองการตั้งค่า
- สำรองผลการเทรดและสถิติ

## ข้อควรระวัง

### ความปลอดภัย

- ใช้กับ Account Demo ก่อน
- เริ่มต้นด้วยเงินทุนน้อย
- ตรวจสอบการตั้งค่า Risk Management

### ความรับผิดชอบ

- EA เป็นเครื่องมือช่วยเทรด ไม่ใช่การรับประกันกำไร
- ใช้ด้วยความระมัดระวังและความรับผิดชอบ
- ตรวจสอบกฎหมายและข้อบังคับของโบรกเกอร์

### การทดสอบ

- ทดสอบกลยุทธ์ด้วย Backtest ก่อน
- ทดสอบในสภาพแวดล้อมจริงด้วย Account Demo
- ปรับแต่งพารามิเตอร์ตามผลลัพธ์

## หมายเหตุสำคัญ

### การใช้งานครั้งแรก

1. **อ่านคู่มือนี้ให้ครบถ้วน**
2. **ทดสอบใน Account Demo ก่อน**
3. **เริ่มต้นด้วยการตั้งค่าเริ่มต้น**
4. **ตรวจสอบการทำงานอย่างละเอียด**

### การพัฒนาต่อ

- ศึกษาโค้ดและเข้าใจการทำงาน
- ปรับแต่งตามความต้องการ
- เพิ่มฟีเจอร์ใหม่ตามต้องการ
- แบ่งปันประสบการณ์กับผู้อื่น

### การสนับสนุน

- คู่มือนี้เป็นเพียงจุดเริ่มต้น
- ศึกษาเพิ่มเติมจากแหล่งข้อมูลอื่นๆ
- ฝึกฝนและทดลองอย่างต่อเนื่อง
- พัฒนาทักษะการเขียนโปรแกรม

## สรุป

MyFirstBot เป็น EA ที่ออกแบบมาสำหรับการศึกษาและทดสอบการเทรดทองคำ XAUUSD โดยใช้กลยุทธ์การรอสัญญาณจาก 3 indicators พร้อมกัน

**จุดเด่น**:

- โครงสร้างโค้ดที่ชัดเจนและเข้าใจง่าย
- ระบบ Risk Management ที่ครอบคลุม
- การใช้งาน Indicators ที่หลากหลาย
- การจัดการ Order ที่ปลอดภัย

**การใช้งาน**:

- ติดตั้งง่ายและรวดเร็ว
- การตั้งค่าที่ยืดหยุ่น
- การทดสอบที่ครอบคลุม
- การบำรุงรักษาที่ง่าย

**การพัฒนา**:

- โครงสร้างที่เหมาะสำหรับการพัฒนาต่อ
- โค้ดที่อ่านง่ายและปรับแต่งได้
- ระบบการจัดการข้อผิดพลาดที่ดี
- การบันทึกและวิเคราะห์ข้อมูล

**คำแนะนำสุดท้าย**: ใช้ด้วยความระมัดระวัง ศึกษาให้เข้าใจก่อนใช้งาน และพัฒนาทักษะอย่างต่อเนื่อง
