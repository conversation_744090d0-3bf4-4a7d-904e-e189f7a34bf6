//+------------------------------------------------------------------+
//|                                              GoldCouterAttact.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "GoldCouterAttact - Volume Profile based trading bot for Gold/XAUUSD"

//--- Include files
#include "../../../core/includes/Constants.mqh"
#include "../../../core/includes/OrderManager.mqh"
#include "../../../core/includes/IndicatorManager.mqh"
#include "../../../core/classes/PerformanceAnalyzer.mqh"

//--- Input Parameters
input group "=== RANGE SETTINGS ==="
input ENUM_RANGE_MODE RangeMode = RANGE_SESSION;           // Range Mode
input string SessionStart = "00:00";                       // Session Start (HH:MM)
input string SessionEnd = "23:59";                         // Session End (HH:MM)
input int BarsBack = 100;                                  // Bars Back (for FIXED_RANGE)
input datetime RangeStart = 0;                             // Range Start (for FIXED_RANGE)
input datetime RangeEnd = 0;                               // Range End (for FIXED_RANGE)

input group "=== VOLUME PROFILE SETTINGS ==="
input int BinSizePoints = 15;                              // Bin Size (points)
input int SmoothWindow = 3;                                // Smoothing Window
input double HVN_percentile = 80.0;                        // HVN Percentile
input double LVN_percentile = 25.0;                        // LVN Percentile
input int MinTicks = 1500;                                 // Minimum Ticks Required

input group "=== ZONE SETTINGS ==="
input double ZoneWidth_ATR_Multiplier = 0.25;              // Zone Width ATR Multiplier
input double TouchTolerance_ATR_Multiplier = 0.25;         // Touch Tolerance ATR Multiplier
input int MaxZones = 10;                                   // Maximum Zones
input int ATR_Period = 14;                                 // ATR Period

input group "=== TRADING SETTINGS ==="
input bool EnableTrading = true;                           // Enable Trading
input double RiskPercent = 2.0;                            // Risk per trade (%)
input double StopLossPips = 50;                            // Stop Loss (pips)
input double TakeProfitPips = 100;                         // Take Profit (pips)
input int MagicNumber = 123456;                            // Magic Number
input string TradeComment = "GoldCouterAttact";            // Trade Comment

input group "=== UPDATE SETTINGS ==="
input int UpdateInterval = 30;                             // Update Interval (seconds)
input bool ExportToFile = true;                            // Export to File
input bool DrawZones = true;                               // Draw Zones on Chart

//--- Enums
enum ENUM_RANGE_MODE
{
   RANGE_SESSION,    // SESSION
   RANGE_FIXED       // FIXED_RANGE
};

enum ENUM_ZONE_TYPE
{
   ZONE_HVN = 1,    // High Volume Node
   ZONE_LVN = 2     // Low Volume Node
};

enum ENUM_ZONE_STATUS
{
   ZONE_FRESH = 1,  // Fresh Zone
   ZONE_USED = 2,   // Used Zone
   ZONE_STALE = 3   // Stale Zone
};

//--- Structures
struct SZone
{
   double mid;           // Zone center price
   double upper;         // Zone upper boundary
   double lower;         // Zone lower boundary
   ENUM_ZONE_TYPE type;  // Zone type (HVN/LVN)
   ENUM_ZONE_STATUS status; // Zone status
   double score;         // Zone score (0-10)
   datetime created_at;  // Creation timestamp
   int volume;           // Volume at this zone
   bool touched;         // Has been touched
};

//--- Global Variables
COrderManager orderManager;               // Order Manager
CIndicatorManager indicatorManager;       // Indicator Manager
CPerformanceAnalyzer performanceAnalyzer; // Performance Analyzer

SZone zones[];           // Array of zones
datetime lastProfileUpdate = 0;  // Last profile update time
datetime currentRangeStart = 0;  // Current range start
datetime currentRangeEnd = 0;    // Current range end
bool isNewSession = false;       // New session flag
int totalTicks = 0;              // Total ticks processed

//--- Global variables for volume profile
double globalMinPrice = 0;
double globalMaxPrice = 0;
double globalBinSize = 0;
int globalBinCount = 0;
int globalVolume[];
double globalSmoothedVolume[];

datetime lastBarTime;                     // Last bar time
bool isNewBar;                           // New bar flag

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== GoldCouterAttact Initialization ===");
   
   // Initialize Order Manager
   orderManager.SetSymbol(Symbol());
   orderManager.SetStopLoss(StopLossPips * _Point);
   orderManager.SetTakeProfit(TakeProfitPips * _Point);
   
   // Calculate lot size based on risk
   double lotSize = orderManager.CalculateLotSize(RiskPercent, StopLossPips);
   orderManager.SetLotSize(lotSize);
   
   Print("Calculated Lot Size: ", lotSize);
   Print("Stop Loss: ", StopLossPips, " pips");
   Print("Take Profit: ", TakeProfitPips, " pips");
   
   // Initialize Indicator Manager
   if(!indicatorManager.Initialize())
   {
      Print("Error: Cannot initialize Indicators");
      return INIT_FAILED;
   }
   
   // Initialize Performance Analyzer
   performanceAnalyzer.Initialize();
   
   Print("Indicators initialized successfully");
   
   // Set initial time
   lastBarTime = iTime(Symbol(), Period(), 0);
   
   // Start timer
   EventSetTimer(UpdateInterval);
   
   // Calculate initial profile
   CalculateVolumeProfile();
   
   Print("=== GoldCouterAttact Ready ===");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   EventKillTimer();
   ClearZones();
   
   // Close all orders
   orderManager.CloseAllOrders();
   
   // Save performance statistics
   performanceAnalyzer.SaveStatistics();
   
   Print("=== GoldCouterAttact Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new bar
   if(!IsNewBar())
      return;
   
   // Check if trading is enabled
   if(!EnableTrading)
      return;
   
   // Check for open orders
   if(orderManager.HasOpenOrders())
      return;
   
   // Update zone status
   UpdateZoneStatus();
   
   // Analyze trading signal based on zones
   int signal = AnalyzeTradingSignal();
   
   // Execute trading based on signal
   if(signal == 1) // Buy signal
   {
      if(orderManager.OpenBuyOrder())
      {
         Print("Buy order opened successfully");
         performanceAnalyzer.RecordTrade(ORDER_TYPE_BUY, Ask, StopLossPips, TakeProfitPips);
      }
   }
   else if(signal == -1) // Sell signal
   {
      if(orderManager.OpenSellOrder())
      {
         Print("Sell order opened successfully");
         performanceAnalyzer.RecordTrade(ORDER_TYPE_SELL, Bid, StopLossPips, TakeProfitPips);
      }
   }
}

//+------------------------------------------------------------------+
//| Timer function                                                    |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Update zone status
   UpdateZoneStatus();
   
   // Check if profile should be recalculated
   if(ShouldRecalculateProfile())
   {
      CalculateVolumeProfile();
   }
}

//+------------------------------------------------------------------+
//| Check for new bar                                                |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(Symbol(), Period(), 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      isNewBar = true;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Calculate Volume Profile from Tick Data                          |
//+------------------------------------------------------------------+
void CalculateVolumeProfile()
{
   Print("Calculating Volume Profile...");
   
   // Determine calculation range
   if(!DetermineCalculationRange())
   {
      Print("Failed to determine calculation range");
      return;
   }
   
   // Download tick data
   MqlTick ticks[];
   if(!DownloadTicks(ticks))
   {
      Print("Failed to download tick data");
      return;
   }
   
   // Create volume profile
   if(!CreateVolumeProfile(ticks))
   {
      Print("Failed to create volume profile");
      return;
   }
   
   // Detect nodes and create zones
   if(!CreateZones())
   {
      Print("Failed to create zones");
      return;
   }
   
   // Score and rank zones
   ScoreAndRankZones();
   
   lastProfileUpdate = TimeCurrent();
   Print("Volume Profile calculated successfully. Zones created: ", ArraySize(zones));
}

//+------------------------------------------------------------------+
//| Determine calculation range based on mode                        |
//+------------------------------------------------------------------+
bool DetermineCalculationRange()
{
   if(RangeMode == RANGE_SESSION)
   {
      // Use previous session
      datetime currentTime = TimeCurrent();
      MqlDateTime dt;
      TimeToStruct(currentTime, dt);
      
      // Calculate previous session
      if(dt.hour >= StringToInteger(StringSubstr(SessionStart, 0, 2)) && 
         dt.min >= StringToInteger(StringSubstr(SessionStart, 3, 2)))
      {
         // Current session, use previous
         currentRangeEnd = StringToTime(StringFormat("%04d.%02d.%02d %s", 
                           dt.year, dt.mon, dt.day, SessionStart)) - 1;
         currentRangeStart = StringToTime(StringFormat("%04d.%02d.%02d %s", 
                           dt.year, dt.mon, dt.day, SessionEnd)) + 1;
      }
      else
      {
         // Previous session
         currentRangeEnd = StringToTime(StringFormat("%04d.%02d.%02d %s", 
                           dt.year, dt.mon, dt.day, SessionEnd));
         currentRangeStart = StringToTime(StringFormat("%04d.%02d.%02d %s", 
                           dt.year, dt.mon, dt.day, SessionStart));
      }
   }
   else // RANGE_FIXED
   {
      if(RangeStart != 0 && RangeEnd != 0)
      {
         currentRangeStart = RangeStart;
         currentRangeEnd = RangeEnd;
      }
      else
      {
         // Use bars back
         currentRangeEnd = iTime(_Symbol, PERIOD_CURRENT, 1);
         currentRangeStart = iTime(_Symbol, PERIOD_CURRENT, BarsBack);
      }
   }
   
   Print("Calculation range: ", TimeToString(currentRangeStart), " to ", TimeToString(currentRangeEnd));
   return true;
}

//+------------------------------------------------------------------+
//| Download tick data for the specified range                       |
//+------------------------------------------------------------------+
bool DownloadTicks(MqlTick &ticks[])
{
   Print("Downloading ticks from ", TimeToString(currentRangeStart), " to ", TimeToString(currentRangeEnd));
   
   // Get tick count
   int tickCount = CopyTicksRange(_Symbol, ticks, COPY_TICKS_ALL, 
                                  currentRangeStart * 1000, currentRangeEnd * 1000);
   
   if(tickCount <= 0)
   {
      Print("No ticks found in range");
      return false;
   }
   
   totalTicks = tickCount;
   Print("Downloaded ", tickCount, " ticks");
   
   // Check minimum ticks requirement
   if(tickCount < MinTicks)
   {
      Print("Warning: Only ", tickCount, " ticks found, minimum required: ", MinTicks);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Create volume profile from tick data                             |
//+------------------------------------------------------------------+
bool CreateVolumeProfile(const MqlTick &ticks[])
{
   if(ArraySize(ticks) == 0) return false;
   
   // Find price range
   double minPrice = DBL_MAX;
   double maxPrice = DBL_MIN;
   
   for(int idx = 0; idx < ArraySize(ticks); idx++)
   {
      double price = GetTickPrice(ticks[idx]);
      if(price > 0)
      {
         minPrice = MathMin(minPrice, price);
         maxPrice = MathMax(maxPrice, price);
      }
   }
   
   if(minPrice == DBL_MAX || maxPrice == DBL_MIN) return false;
   
   // Calculate bin size
   double binSize = BinSizePoints * _Point;
   int binCount = (int)MathFloor((maxPrice - minPrice) / binSize) + 1;
   
   if(binCount <= 0) return false;
   
   // Create volume array
   int volume[];
   ArrayResize(volume, binCount);
   ArrayInitialize(volume, 0);
   
   // Count ticks per bin
   for(int idx = 0; idx < ArraySize(ticks); idx++)
   {
      double price = GetTickPrice(ticks[idx]);
      if(price > 0)
      {
         int binIndex = (int)MathFloor((price - minPrice) / binSize);
         if(binIndex >= 0 && binIndex < binCount)
         {
            volume[binIndex]++;
         }
      }
   }
   
   // Smooth volume data
   double smoothedVolume[];
   ArrayResize(smoothedVolume, binCount);
   SmoothVolume(volume, smoothedVolume);
   
   // Store profile data globally for zone creation
   globalMinPrice = minPrice;
   globalMaxPrice = maxPrice;
   globalBinSize = binSize;
   globalBinCount = binCount;
   globalVolume = volume;
   globalSmoothedVolume = smoothedVolume;
   
   return true;
}

//+------------------------------------------------------------------+
//| Get tick price (mid, bid, or last)                              |
//+------------------------------------------------------------------+
double GetTickPrice(const MqlTick &tick)
{
   // Prefer mid price if both bid and ask available
   if(tick.bid > 0 && tick.ask > 0)
   {
      return (tick.bid + tick.ask) / 2.0;
   }
   // Fallback to bid
   else if(tick.bid > 0)
   {
      return tick.bid;
   }
   // Fallback to last
   else if(tick.last > 0)
   {
      return tick.last;
   }
   
   return 0;
}

//+------------------------------------------------------------------+
//| Smooth volume data using SMA                                     |
//+------------------------------------------------------------------+
void SmoothVolume(const int &volume[], double &smoothed[])
{
   int size = ArraySize(volume);
   ArrayResize(smoothed, size);
   
   for(int idx = 0; idx < size; idx++)
   {
      double sum = 0;
      int count = 0;
      
      for(int j = MathMax(0, idx - SmoothWindow + 1); j <= idx; j++)
      {
         sum += volume[j];
         count++;
      }
      
      smoothed[idx] = count > 0 ? sum / count : 0;
   }
}

//+------------------------------------------------------------------+
//| Create zones from volume profile                                 |
//+------------------------------------------------------------------+
bool CreateZones()
{
   if(globalBinCount <= 0) return false;
   
   // Clear existing zones
   ArrayResize(zones, 0);
   
   // Find POC (Point of Control)
   int pocIndex = FindPOC();
   
   // Find HVN and LVN
   int hvnIndices[];
   int lvnIndices[];
   FindHVN(hvnIndices);
   FindLVN(lvnIndices);
   
   // Create zones from nodes
   CreateZonesFromNodes(hvnIndices, ZONE_HVN);
   CreateZonesFromNodes(lvnIndices, ZONE_LVN);
   
   // Merge nearby zones
   MergeNearbyZones();
   
   // Limit to MaxZones
   LimitZones();
   
   return true;
}

//+------------------------------------------------------------------+
//| Find Point of Control (highest volume)                           |
//+------------------------------------------------------------------+
int FindPOC()
{
   int maxIndex = 0;
   double maxVolume = globalSmoothedVolume[0];
   
   for(int idx = 1; idx < globalBinCount; idx++)
   {
      if(globalSmoothedVolume[idx] > maxVolume)
      {
         maxVolume = globalSmoothedVolume[idx];
         maxIndex = idx;
      }
   }
   
   return maxIndex;
}

//+------------------------------------------------------------------+
//| Find High Volume Nodes                                           |
//+------------------------------------------------------------------+
void FindHVN(int &indices[])
{
   ArrayResize(indices, 0);
   
   // Calculate percentile threshold
   double threshold = CalculatePercentile(globalSmoothedVolume, HVN_percentile);
   
   // Find local maxima above threshold
   for(int idx = 1; idx < globalBinCount - 1; idx++)
   {
      if(globalSmoothedVolume[idx] >= threshold &&
         globalSmoothedVolume[idx] > globalSmoothedVolume[idx-1] &&
         globalSmoothedVolume[idx] > globalSmoothedVolume[idx+1])
      {
         int size = ArraySize(indices);
         ArrayResize(indices, size + 1);
         indices[size] = idx;
      }
   }
}

//+------------------------------------------------------------------+
//| Find Low Volume Nodes                                            |
//+------------------------------------------------------------------+
void FindLVN(int &indices[])
{
   ArrayResize(indices, 0);
   
   // Calculate percentile threshold
   double threshold = CalculatePercentile(globalSmoothedVolume, LVN_percentile);
   
   // Find local minima below threshold
   for(int idx = 1; idx < globalBinCount - 1; idx++)
   {
      if(globalSmoothedVolume[idx] <= threshold &&
         globalSmoothedVolume[idx] < globalSmoothedVolume[idx-1] &&
         globalSmoothedVolume[idx] < globalSmoothedVolume[idx+1])
      {
         int size = ArraySize(indices);
         ArrayResize(indices, size + 1);
         indices[size] = idx;
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate percentile of array                                    |
//+------------------------------------------------------------------+
double CalculatePercentile(const double &data[], double percentile)
{
   int size = ArraySize(data);
   if(size == 0) return 0;
   
   // Create copy and sort
   double sorted[];
   ArrayCopy(sorted, data);
   ArraySort(sorted);
   
   // Calculate percentile
   int index = (int)MathFloor(percentile / 100.0 * (size - 1));
   return sorted[index];
}

//+------------------------------------------------------------------+
//| Create zones from node indices                                   |
//+------------------------------------------------------------------+
void CreateZonesFromNodes(const int &indices[], ENUM_ZONE_TYPE zoneType)
{
   for(int idx = 0; idx < ArraySize(indices); idx++)
   {
      int binIndex = indices[idx];
      
      // Calculate zone center price
      double centerPrice = globalMinPrice + binIndex * globalBinSize + globalBinSize / 2.0;
      
      // Calculate zone width based on ATR
      double atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period, 1);
      double zoneWidth = MathMax(ZoneWidth_ATR_Multiplier * atr, 2 * BinSizePoints * _Point);
      
      // Create zone
      SZone zone;
      zone.mid = centerPrice;
      zone.upper = centerPrice + zoneWidth / 2.0;
      zone.lower = centerPrice - zoneWidth / 2.0;
      zone.type = zoneType;
      zone.status = ZONE_FRESH;
      zone.score = 0;
      zone.created_at = currentRangeEnd;
      zone.volume = globalVolume[binIndex];
      zone.touched = false;
      
      // Add to zones array
      int size = ArraySize(zones);
      ArrayResize(zones, size + 1);
      zones[size] = zone;
   }
}

//+------------------------------------------------------------------+
//| Merge nearby zones                                               |
//+------------------------------------------------------------------+
void MergeNearbyZones()
{
   int size = ArraySize(zones);
   if(size <= 1) return;
   
   // Sort zones by price
   ArraySort(zones, WHOLE_ARRAY, 0, MODE_ASCEND);
   
   // Merge nearby zones
   for(int idx = 0; idx < size; idx++)
   {
      if(zones[idx].status == ZONE_STALE) continue;
      
      for(int j = idx + 1; j < size; j++)
      {
         if(zones[j].status == ZONE_STALE) continue;
         
         // Check if zones are close
         double distance = MathAbs(zones[idx].mid - zones[j].mid);
         double atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period, 1);
         
         if(distance < 0.25 * atr)
         {
            // Merge zones (keep the one with higher volume)
            if(zones[idx].volume >= zones[j].volume)
            {
               zones[j].status = ZONE_STALE;
            }
            else
            {
               zones[idx].status = ZONE_STALE;
               break;
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Limit zones to MaxZones                                          |
//+------------------------------------------------------------------+
void LimitZones()
{
   int size = ArraySize(zones);
   if(size <= MaxZones) return;
   
   // Sort by score (descending)
   ArraySort(zones, WHOLE_ARRAY, 0, MODE_DESCEND);
   
   // Keep only MaxZones
   ArrayResize(zones, MaxZones);
}

//+------------------------------------------------------------------+
//| Score and rank zones                                             |
//+------------------------------------------------------------------+
void ScoreAndRankZones()
{
   int size = ArraySize(zones);
   if(size == 0) return;
   
   double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
   double maxVolume = 0;
   
   // Find maximum volume for normalization
   for(int idx = 0; idx < size; idx++)
   {
      maxVolume = MathMax(maxVolume, zones[idx].volume);
   }
   
   // Calculate scores
   for(int idx = 0; idx < size; idx++)
   {
      if(zones[idx].status == ZONE_STALE) continue;
      
      // Prominence (volume-based)
      double prominence = maxVolume > 0 ? zones[idx].volume / maxVolume : 0;
      
      // Proximity to current price
      double atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period, 1);
      double proximity = MathMax(0, 2 - MathAbs(currentPrice - zones[idx].mid) / atr);
      proximity = MathMin(proximity, 2);
      
      // Type bonus (LVN gets bonus)
      double typeBonus = (zones[idx].type == ZONE_LVN) ? 1.2 : 1.0;
      
      // Freshness bonus
      double freshBonus = (zones[idx].status == ZONE_FRESH) ? 0.5 : 0.0;
      
      // Calculate final score
      zones[idx].score = 0.6 * prominence + 0.3 * proximity + 0.1 * typeBonus + freshBonus;
      zones[idx].score = MathMin(zones[idx].score, 10.0);
   }
   
   // Sort by score
   ArraySort(zones, WHOLE_ARRAY, 0, MODE_DESCEND);
}

//+------------------------------------------------------------------+
//| Update zone status based on current price action                 |
//+------------------------------------------------------------------+
void UpdateZoneStatus()
{
   int size = ArraySize(zones);
   if(size == 0) return;
   
   double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
   double atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period, 1);
   double touchTolerance = TouchTolerance_ATR_Multiplier * atr;
   
   for(int idx = 0; idx < size; idx++)
   {
      if(zones[idx].status != ZONE_FRESH) continue;
      
      // Check if zone is touched
      if(currentPrice >= zones[idx].lower - touchTolerance && 
         currentPrice <= zones[idx].upper + touchTolerance)
      {
         zones[idx].touched = true;
         zones[idx].status = ZONE_USED;
         Print("Zone ", idx, " touched and marked as USED");
      }
   }
}

//+------------------------------------------------------------------+
//| Check if new session started                                     |
//+------------------------------------------------------------------+
bool CheckNewSession()
{
   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);
   
   string currentSession = StringFormat("%02d:%02d", dt.hour, dt.min);
   
   if(currentSession == SessionStart)
   {
      if(!isNewSession)
      {
         isNewSession = true;
         return true;
      }
   }
   else
   {
      isNewSession = false;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Check if profile should be recalculated                          |
//+------------------------------------------------------------------+
bool ShouldRecalculateProfile()
{
   if(RangeMode == RANGE_SESSION)
   {
      // Recalculate at session start
      return CheckNewSession();
   }
   else
   {
      // Recalculate every day for FIXED_RANGE
      datetime currentTime = TimeCurrent();
      return (currentTime - lastProfileUpdate) > 86400; // 24 hours
   }
}

//+------------------------------------------------------------------+
//| Analyze trading signal based on zones                            |
//+------------------------------------------------------------------+
int AnalyzeTradingSignal()
{
   int size = ArraySize(zones);
   if(size == 0) return 0;
   
   double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
   double bestScore = 0;
   int bestZoneIndex = -1;
   
   // Find the best FRESH zone
   for(int idx = 0; idx < size; idx++)
   {
      if(zones[idx].status == ZONE_FRESH && zones[idx].score > bestScore)
      {
         bestScore = zones[idx].score;
         bestZoneIndex = idx;
      }
   }
   
   if(bestZoneIndex == -1) return 0;
   
   SZone &bestZone = zones[bestZoneIndex];
   
   // Check if price is near the zone
   double atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period, 1);
   double touchTolerance = TouchTolerance_ATR_Multiplier * atr;
   
   if(currentPrice >= bestZone.lower - touchTolerance && 
      currentPrice <= bestZone.upper + touchTolerance)
   {
      // Price is in the zone, generate signal based on zone type
      if(bestZone.type == ZONE_HVN)
      {
         // HVN: Look for reversal (bounce)
         if(currentPrice < bestZone.mid)
            return 1; // Buy signal
         else
            return -1; // Sell signal
      }
      else // ZONE_LVN
      {
         // LVN: Look for breakout
         if(currentPrice > bestZone.upper)
            return 1; // Buy signal
         else if(currentPrice < bestZone.lower)
            return -1; // Sell signal
      }
   }
   
   return 0; // No signal
}

//+------------------------------------------------------------------+
//| Draw zones on chart                                              |
//+------------------------------------------------------------------+
void DrawZonesOnChart()
{
   if(!DrawZones) return;
   
   // Clear existing objects
   ObjectsDeleteAll(0, "Zone_");
   
   int size = ArraySize(zones);
   for(int idx = 0; idx < size; idx++)
   {
      if(zones[idx].status == ZONE_STALE) continue;
      
      string objName = "Zone_" + IntegerToString(idx);
      color zoneColor = (zones[idx].type == ZONE_HVN) ? clrRed : clrGreen;
      
      // Create rectangle
      ObjectCreate(0, objName, OBJ_RECTANGLE, 0, 
                   zones[idx].created_at, zones[idx].upper,
                   TimeCurrent(), zones[idx].lower);
      
      ObjectSetInteger(0, objName, OBJPROP_COLOR, zoneColor);
      ObjectSetInteger(0, objName, OBJPROP_FILL, true);
      ObjectSetInteger(0, objName, OBJPROP_BACK, true);
      ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
      
      // Create label
      string labelName = "Label_" + IntegerToString(idx);
      string statusText = (zones[idx].status == ZONE_FRESH) ? "FRESH" : "USED";
      string typeText = (zones[idx].type == ZONE_HVN) ? "HVN" : "LVN";
      
      ObjectCreate(0, labelName, OBJ_TEXT, 0, 
                   zones[idx].created_at, zones[idx].upper);
      
      ObjectSetString(0, labelName, OBJPROP_TEXT, 
                      typeText + " " + statusText + " (score=" + DoubleToString(zones[idx].score, 1) + ")");
      ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrWhite);
      ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
   }
}

//+------------------------------------------------------------------+
//| Export zones to file                                             |
//+------------------------------------------------------------------+
void ExportZonesToFile()
{
   if(!ExportToFile) return;
   
   int size = ArraySize(zones);
   if(size == 0) return;
   
   string fileName = "fresh_zones_" + _Symbol + "_" + EnumToString(Period()) + ".csv";
   int fileHandle = FileOpen(fileName, FILE_WRITE | FILE_CSV);
   
   if(fileHandle != INVALID_HANDLE)
   {
      // Write header
      FileWrite(fileHandle, "Timestamp", "Type", "Mid", "Upper", "Lower", "Status", "Score", "Volume");
      
      // Write zone data
      for(int idx = 0; idx < size; idx++)
      {
         if(zones[idx].status == ZONE_STALE) continue;
         
         string typeText = (zones[idx].type == ZONE_HVN) ? "HVN" : "LVN";
         string statusText = (zones[idx].status == ZONE_FRESH) ? "FRESH" : "USED";
         
         FileWrite(fileHandle, 
                   TimeToString(zones[idx].created_at),
                   typeText,
                   DoubleToString(zones[idx].mid, _Digits),
                   DoubleToString(zones[idx].upper, _Digits),
                   DoubleToString(zones[idx].lower, _Digits),
                   statusText,
                   DoubleToString(zones[idx].score, 2),
                   IntegerToString(zones[idx].volume));
      }
      
      FileClose(fileHandle);
      Print("Zones exported to ", fileName);
   }
}

//+------------------------------------------------------------------+
//| Clear zones                                                      |
//+------------------------------------------------------------------+
void ClearZones()
{
   ArrayResize(zones, 0);
   ObjectsDeleteAll(0, "Zone_");
   ObjectsDeleteAll(0, "Label_");
}
