//+------------------------------------------------------------------+
//|                                                TradingEngine.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Trading Engine Class                                             |
//+------------------------------------------------------------------+
class CTradingEngine
{
private:
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;
   
   // Trading parameters
   double            m_lot_size;
   double            m_max_risk_percent;
   double            m_risk_reward_ratio;
   int               m_max_positions;
   int               m_magic_number;
   
   // Position tracking
   int               m_current_positions;
   double            m_daily_profit;
   double            m_daily_loss;
   datetime          m_last_trade_time;
   
   // Zone tracking
   double            m_last_support_price;
   double            m_last_resistance_price;
   datetime          m_last_zone_touch_time;
   
   // Risk management
   double            m_max_daily_loss;
   double            m_max_daily_profit;
   bool              m_trading_allowed;
   
   // Helper methods
   bool              IsNewBar();
   double            CalculateLotSize(double stop_loss_points);
   bool              CheckRiskLimits();
   bool              IsValidEntry(const ZoneInfo &zone, ENUM_ORDER_TYPE order_type);
   double            CalculateStopLoss(const ZoneInfo &zone, ENUM_ORDER_TYPE order_type);
   double            CalculateTakeProfit(double entry_price, double stop_loss, ENUM_ORDER_TYPE order_type);
   bool              CheckTimeFilter();
   bool              CheckSpreadFilter();
   
public:
   // Constructor/Destructor
                     CTradingEngine(string symbol, ENUM_TIMEFRAMES timeframe);
                    ~CTradingEngine();
   
   // Configuration
   void              SetLotSize(double lot_size) { m_lot_size = lot_size; }
   void              SetRiskPercent(double risk_percent) { m_max_risk_percent = risk_percent; }
   void              SetRiskRewardRatio(double ratio) { m_risk_reward_ratio = ratio; }
   void              SetMaxPositions(int max_positions) { m_max_positions = max_positions; }
   void              SetMagicNumber(int magic) { m_magic_number = magic; }
   void              SetDailyLimits(double max_loss, double max_profit);
   
   // Main trading methods
   bool              ProcessZoneSignal(const ZoneInfo &zone, double current_price);
   bool              CheckExitSignals();
   void              UpdatePositions();
   void              CloseAllPositions(string reason = "");
   
   // Entry signals
   bool              CheckBuySignal(const ZoneInfo &support_zone, double current_price);
   bool              CheckSellSignal(const ZoneInfo &resistance_zone, double current_price);
   
   // Order management
   bool              OpenBuyOrder(const ZoneInfo &zone, double current_price);
   bool              OpenSellOrder(const ZoneInfo &zone, double current_price);
   bool              ModifyPosition(int ticket, double new_sl, double new_tp);
   bool              ClosePosition(int ticket, string reason = "");
   
   // Position analysis
   int               GetCurrentPositions();
   double            GetFloatingPL();
   double            GetDailyPL();
   bool              HasPositionAtZone(const ZoneInfo &zone);
   
   // Risk management
   bool              IsRiskAcceptable(double lot_size, double stop_loss_points);
   void              UpdateDailyStats();
   void              ResetDailyStats();
   
   // Statistics
   void              PrintTradingStats();
   double            GetWinRate();
   double            GetProfitFactor();
   
   // Status
   bool              IsTradingAllowed() const { return m_trading_allowed; }
   void              EnableTrading() { m_trading_allowed = true; }
   void              DisableTrading() { m_trading_allowed = false; }
   
   // Missing methods
   bool              CheckBearishReversal();
   bool              CheckBullishReversal();
   bool              CheckTrailingStop(ulong ticket);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CTradingEngine::CTradingEngine(string symbol, ENUM_TIMEFRAMES timeframe)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   
   // Default parameters
   m_lot_size = 0.01;
   m_max_risk_percent = 2.0;
   m_risk_reward_ratio = 2.0;
   m_max_positions = 3;
   m_magic_number = 12345;
   
   // Initialize tracking
   m_current_positions = 0;
   m_daily_profit = 0;
   m_daily_loss = 0;
   m_last_trade_time = 0;
   
   m_last_support_price = 0;
   m_last_resistance_price = 0;
   m_last_zone_touch_time = 0;
   
   // Risk limits
   m_max_daily_loss = 1000.0;  // USD
   m_max_daily_profit = 3000.0; // USD
   m_trading_allowed = true;
   
   Print("Trading Engine initialized for ", m_symbol);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CTradingEngine::~CTradingEngine()
{
   Print("Trading Engine destroyed");
}

//+------------------------------------------------------------------+
//| Set daily risk limits                                            |
//+------------------------------------------------------------------+
void CTradingEngine::SetDailyLimits(double max_loss, double max_profit)
{
   m_max_daily_loss = max_loss;
   m_max_daily_profit = max_profit;
   
   Print("Daily limits set - Max Loss: $", max_loss, ", Max Profit: $", max_profit);
}

//+------------------------------------------------------------------+
//| Process zone signal for potential trade                          |
//+------------------------------------------------------------------+
bool CTradingEngine::ProcessZoneSignal(const ZoneInfo &zone, double current_price)
{
   if(!m_trading_allowed || !CheckRiskLimits())
      return false;
   
   // Check if we already have position at this zone
   if(HasPositionAtZone(zone))
      return false;
   
   // Check maximum positions limit
   if(GetCurrentPositions() >= m_max_positions)
      return false;
   
   // Check time and spread filters
   if(!CheckTimeFilter() || !CheckSpreadFilter())
      return false;
   
   bool signal_processed = false;
   
   // Process support zone (buy signal)
   if(zone.type == ZONE_HVN || zone.type == ZONE_LVN)
   {
      if(current_price <= zone.upper_price && current_price >= zone.lower_price)
      {
         if(CheckBuySignal(zone, current_price))
         {
            signal_processed = OpenBuyOrder(zone, current_price);
         }
      }
   }
   
   // Process resistance zone (sell signal)
   if(zone.type == ZONE_HVN || zone.type == ZONE_LVN)
   {
      if(current_price <= zone.upper_price && current_price >= zone.lower_price)
      {
         if(CheckSellSignal(zone, current_price))
         {
            signal_processed = OpenSellOrder(zone, current_price);
         }
      }
   }
   
   return signal_processed;
}

//+------------------------------------------------------------------+
//| Check buy signal at support zone                                 |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckBuySignal(const ZoneInfo &zone, double current_price)
{
   // Zone must be fresh or tested but not broken
   if(zone.status != ZONE_FRESH && zone.status != ZONE_TESTED)
      return false;
   
   // Price should be near the lower boundary of support zone
   double zone_height = zone.upper_price - zone.lower_price;
   double entry_threshold = zone.lower_price + (zone_height * 0.3); // Enter in lower 30% of zone
   
   if(current_price > entry_threshold)
      return false;
   
   // Check for bullish price action
   double high = iHigh(m_symbol, m_timeframe, 0);
   double low = iLow(m_symbol, m_timeframe, 0);
   double close = iClose(m_symbol, m_timeframe, 0);
   double open = iOpen(m_symbol, m_timeframe, 0);
   
   // Look for hammer/doji patterns or bullish engulfing
   double body_size = MathAbs(close - open);
   double candle_range = high - low;
   double lower_wick = MathMin(open, close) - low;
   
   // Hammer pattern: long lower wick, small body
   bool is_hammer = (lower_wick > body_size * 2) && (body_size < candle_range * 0.3);
   
   // Bullish engulfing (simplified)
   double prev_close = iClose(m_symbol, m_timeframe, 1);
   double prev_open = iOpen(m_symbol, m_timeframe, 1);
   bool is_bullish_engulfing = (prev_close < prev_open) && (close > open) && 
                               (close > prev_open) && (open < prev_close);
   
   return is_hammer || is_bullish_engulfing || (close > open && lower_wick > body_size);
}

//+------------------------------------------------------------------+
//| Check sell signal at resistance zone                             |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckSellSignal(const ZoneInfo &zone, double current_price)
{
   // Zone must be fresh or tested but not broken
   if(zone.status != ZONE_FRESH && zone.status != ZONE_TESTED)
      return false;
   
   // Price should be near the upper boundary of resistance zone
   double zone_height = zone.upper_price - zone.lower_price;
   double entry_threshold = zone.upper_price - (zone_height * 0.3); // Enter in upper 30% of zone
   
   if(current_price < entry_threshold)
      return false;
   
   // Check for bearish price action
   double high = iHigh(m_symbol, m_timeframe, 0);
   double low = iLow(m_symbol, m_timeframe, 0);
   double close = iClose(m_symbol, m_timeframe, 0);
   double open = iOpen(m_symbol, m_timeframe, 0);
   
   // Look for shooting star/doji patterns or bearish engulfing
   double body_size = MathAbs(close - open);
   double candle_range = high - low;
   double upper_wick = high - MathMax(open, close);
   
   // Shooting star pattern: long upper wick, small body
   bool is_shooting_star = (upper_wick > body_size * 2) && (body_size < candle_range * 0.3);
   
   // Bearish engulfing (simplified)
   double prev_close = iClose(m_symbol, m_timeframe, 1);
   double prev_open = iOpen(m_symbol, m_timeframe, 1);
   bool is_bearish_engulfing = (prev_close > prev_open) && (close < open) && 
                               (close < prev_open) && (open > prev_close);
   
   return is_shooting_star || is_bearish_engulfing || (close < open && upper_wick > body_size);
}

//+------------------------------------------------------------------+
//| Open buy order at support zone                                   |
//+------------------------------------------------------------------+
bool CTradingEngine::OpenBuyOrder(const ZoneInfo &zone, double current_price)
{
   double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   double stop_loss = CalculateStopLoss(zone, ORDER_TYPE_BUY);
   double take_profit = CalculateTakeProfit(ask, stop_loss, ORDER_TYPE_BUY);

   double stop_loss_points = (ask - stop_loss) / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double lot_size = CalculateLotSize(stop_loss_points);

   if(!IsRiskAcceptable(lot_size, stop_loss_points))
   {
      Print("Risk too high for buy order at zone ", zone.mid_price);
      return false;
   }

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = m_symbol;
   request.volume = lot_size;
   request.type = ORDER_TYPE_BUY;
   request.price = ask;
   request.sl = stop_loss;
   request.tp = take_profit;
   request.magic = m_magic_number;
   request.comment = "CounterAttack Buy @ " + DoubleToString(zone.mid_price, 5);

   if(OrderSend(request, result))
   {
      if(result.retcode == TRADE_RETCODE_DONE)
      {
         Print("BUY order opened: Ticket=", result.order,
               " Price=", result.price,
               " SL=", stop_loss,
               " TP=", take_profit,
               " Lot=", lot_size);

         m_current_positions++;
         m_last_trade_time = TimeCurrent();
         m_last_support_price = zone.mid_price;

         return true;
      }
      else
      {
         Print("Buy order failed: ", result.retcode, " - ", result.comment);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Open sell order at resistance zone                               |
//+------------------------------------------------------------------+
bool CTradingEngine::OpenSellOrder(const ZoneInfo &zone, double current_price)
{
   double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   double stop_loss = CalculateStopLoss(zone, ORDER_TYPE_SELL);
   double take_profit = CalculateTakeProfit(bid, stop_loss, ORDER_TYPE_SELL);

   double stop_loss_points = (stop_loss - bid) / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double lot_size = CalculateLotSize(stop_loss_points);

   if(!IsRiskAcceptable(lot_size, stop_loss_points))
   {
      Print("Risk too high for sell order at zone ", zone.mid_price);
      return false;
   }

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = m_symbol;
   request.volume = lot_size;
   request.type = ORDER_TYPE_SELL;
   request.price = bid;
   request.sl = stop_loss;
   request.tp = take_profit;
   request.magic = m_magic_number;
   request.comment = "CounterAttack Sell @ " + DoubleToString(zone.mid_price, 5);

   if(OrderSend(request, result))
   {
      if(result.retcode == TRADE_RETCODE_DONE)
      {
         Print("SELL order opened: Ticket=", result.order,
               " Price=", result.price,
               " SL=", stop_loss,
               " TP=", take_profit,
               " Lot=", lot_size);

         m_current_positions++;
         m_last_trade_time = TimeCurrent();
         m_last_resistance_price = zone.mid_price;

         return true;
      }
      else
      {
         Print("Sell order failed: ", result.retcode, " - ", result.comment);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on zone and order type                 |
//+------------------------------------------------------------------+
double CTradingEngine::CalculateStopLoss(const ZoneInfo &zone, ENUM_ORDER_TYPE order_type)
{
   double atr_handle = iATR(m_symbol, m_timeframe, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default

   if(CopyBuffer(atr_handle, 0, 0, 1, atr_values) > 0)
      atr = atr_values[0];
   IndicatorRelease(atr_handle);

   double stop_loss;
   double buffer = atr * 0.5; // 50% of ATR as buffer

   if(order_type == ORDER_TYPE_BUY)
   {
      // For buy orders, SL below the support zone
      stop_loss = zone.lower_price - buffer;
   }
   else // ORDER_TYPE_SELL
   {
      // For sell orders, SL above the resistance zone
      stop_loss = zone.upper_price + buffer;
   }

   return stop_loss;
}

//+------------------------------------------------------------------+
//| Calculate take profit based on risk-reward ratio                 |
//+------------------------------------------------------------------+
double CTradingEngine::CalculateTakeProfit(double entry_price, double stop_loss, ENUM_ORDER_TYPE order_type)
{
   double risk_distance = MathAbs(entry_price - stop_loss);
   double reward_distance = risk_distance * m_risk_reward_ratio;

   double take_profit;

   if(order_type == ORDER_TYPE_BUY)
   {
      take_profit = entry_price + reward_distance;
   }
   else // ORDER_TYPE_SELL
   {
      take_profit = entry_price - reward_distance;
   }

   return take_profit;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                      |
//+------------------------------------------------------------------+
double CTradingEngine::CalculateLotSize(double stop_loss_points)
{
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * (m_max_risk_percent / 100.0);

   double point_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);

   // Calculate value per point
   double value_per_point = point_value * (point / tick_size);

   // Calculate lot size
   double lot_size = risk_amount / (stop_loss_points * value_per_point);

   // Apply lot size limits
   double min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);

   // Round to lot step
   lot_size = MathFloor(lot_size / lot_step) * lot_step;

   // Apply limits
   lot_size = MathMax(lot_size, min_lot);
   lot_size = MathMin(lot_size, max_lot);

   // Additional safety: never risk more than configured lot size
   if(m_lot_size > 0)
      lot_size = MathMin(lot_size, m_lot_size);

   return lot_size;
}

//+------------------------------------------------------------------+
//| Check if risk is acceptable for the trade                        |
//+------------------------------------------------------------------+
bool CTradingEngine::IsRiskAcceptable(double lot_size, double stop_loss_points)
{
   if(lot_size <= 0 || stop_loss_points <= 0)
      return false;

   // Check if we're within daily loss limit
   if(m_daily_loss >= m_max_daily_loss)
   {
      Print("Daily loss limit reached: $", m_daily_loss);
      return false;
   }

   // Check if we've reached daily profit target
   if(m_daily_profit >= m_max_daily_profit)
   {
      Print("Daily profit target reached: $", m_daily_profit);
      return false;
   }

   // Calculate potential loss for this trade
   double point_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double value_per_point = point_value * (point / tick_size);

   double potential_loss = lot_size * stop_loss_points * value_per_point;

   // Check if potential loss exceeds risk per trade
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double max_risk_per_trade = account_balance * (m_max_risk_percent / 100.0);

   if(potential_loss > max_risk_per_trade)
   {
      Print("Potential loss $", potential_loss, " exceeds max risk per trade $", max_risk_per_trade);
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check risk limits before trading                                 |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckRiskLimits()
{
   // Update daily stats first
   UpdateDailyStats();

   // Check daily loss limit
   if(m_daily_loss >= m_max_daily_loss)
   {
      if(m_trading_allowed)
      {
         Print("TRADING DISABLED: Daily loss limit reached $", m_daily_loss);
         DisableTrading();
      }
      return false;
   }

   // Check daily profit target
   if(m_daily_profit >= m_max_daily_profit)
   {
      if(m_trading_allowed)
      {
         Print("TRADING DISABLED: Daily profit target reached $", m_daily_profit);
         DisableTrading();
      }
      return false;
   }

   // Check account equity
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown_percent = ((balance - equity) / balance) * 100.0;

   if(drawdown_percent > 10.0) // 10% max drawdown
   {
      Print("TRADING DISABLED: Drawdown too high: ", drawdown_percent, "%");
      DisableTrading();
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Update daily statistics                                           |
//+------------------------------------------------------------------+
void CTradingEngine::UpdateDailyStats()
{
   static datetime last_update_day = 0;
   datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400; // Start of day

   // Reset stats if new day
   if(current_day != last_update_day)
   {
      ResetDailyStats();
      last_update_day = current_day;
   }

   // Calculate current day P&L
   m_daily_profit = 0;
   m_daily_loss = 0;

   datetime day_start = current_day;
   datetime day_end = day_start + 86400;

   // Get closed positions for today
   if(HistorySelect(day_start, day_end))
   {
      int total_deals = HistoryDealsTotal();

      for(int i = 0; i < total_deals; i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(ticket > 0)
         {
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == m_magic_number &&
               HistoryDealGetString(ticket, DEAL_SYMBOL) == m_symbol)
            {
               double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);

               if(profit > 0)
                  m_daily_profit += profit;
               else
                  m_daily_loss += MathAbs(profit);
            }
         }
      }
   }

   // Add floating P&L
   double floating_pl = GetFloatingPL();
   if(floating_pl > 0)
      m_daily_profit += floating_pl;
   else
      m_daily_loss += MathAbs(floating_pl);
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                            |
//+------------------------------------------------------------------+
void CTradingEngine::ResetDailyStats()
{
   m_daily_profit = 0;
   m_daily_loss = 0;

   // Re-enable trading for new day
   EnableTrading();

   Print("Daily stats reset for new trading day");
}

//+------------------------------------------------------------------+
//| Get current number of positions                                  |
//+------------------------------------------------------------------+
int CTradingEngine::GetCurrentPositions()
{
   int count = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == m_symbol &&
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            count++;
         }
      }
   }

   m_current_positions = count;
   return count;
}

//+------------------------------------------------------------------+
//| Get floating profit/loss                                         |
//+------------------------------------------------------------------+
double CTradingEngine::GetFloatingPL()
{
   double total_pl = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == m_symbol &&
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            total_pl += PositionGetDouble(POSITION_PROFIT);
         }
      }
   }

   return total_pl;
}

//+------------------------------------------------------------------+
//| Check if we have position at specific zone                       |
//+------------------------------------------------------------------+
bool CTradingEngine::HasPositionAtZone(const ZoneInfo &zone)
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == m_symbol &&
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);

            // Check if position was opened within this zone
            if(open_price >= zone.lower_price && open_price <= zone.upper_price)
               return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check time filter (trading hours)                                |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckTimeFilter()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);

   // Allow trading during major sessions
   // London: 8-17 GMT, New York: 13-22 GMT, Asian: 23-8 GMT
   int hour = dt.hour;

   // Skip weekends
   if(dt.day_of_week == 0 || dt.day_of_week == 6)
      return false;

   // Allow trading during active hours (avoid low liquidity periods)
   if(hour >= 1 && hour <= 22) // Avoid 23:00-00:59 GMT
      return true;

   return false;
}

//+------------------------------------------------------------------+
//| Check spread filter                                              |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckSpreadFilter()
{
   double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   double spread = ask - bid;
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);

   double spread_points = spread / point;

   // Get average spread (simplified - use ATR as reference)
   int atr_handle = iATR(m_symbol, m_timeframe, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001;

   if(CopyBuffer(atr_handle, 0, 0, 1, atr_values) > 0)
      atr = atr_values[0];
   IndicatorRelease(atr_handle);

   double max_spread = (atr / point) * 0.1; // Max 10% of ATR

   if(spread_points > max_spread)
   {
      Print("Spread too high: ", spread_points, " points (max: ", max_spread, ")");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check exit signals for open positions                            |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckExitSignals()
{
   bool signals_processed = false;

   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == m_symbol &&
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(m_symbol, SYMBOL_BID) :
                                   SymbolInfoDouble(m_symbol, SYMBOL_ASK);

            // Check for manual exit conditions
            bool should_exit = false;
            string exit_reason = "";

            // 1. Check for opposite zone touch
            if(pos_type == POSITION_TYPE_BUY)
            {
               // Check if price reached a strong resistance zone
               if(current_price > open_price * 1.02) // 2% profit minimum before checking exits
               {
                  // Look for bearish reversal signals
                  if(CheckBearishReversal())
                  {
                     should_exit = true;
                     exit_reason = "Bearish reversal detected";
                  }
               }
            }
            else // POSITION_TYPE_SELL
            {
               // Check if price reached a strong support zone
               if(current_price < open_price * 0.98) // 2% profit minimum before checking exits
               {
                  // Look for bullish reversal signals
                  if(CheckBullishReversal())
                  {
                     should_exit = true;
                     exit_reason = "Bullish reversal detected";
                  }
               }
            }

            // 2. Check for time-based exit (holding too long)
            datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
            datetime current_time = TimeCurrent();
            int holding_hours = (int)((current_time - open_time) / 3600);

            if(holding_hours > 24) // Close after 24 hours
            {
               should_exit = true;
               exit_reason = "Time-based exit (24h limit)";
            }

            // 3. Check for trailing stop
            if(!should_exit && CheckTrailingStop(ticket))
            {
               should_exit = true;
               exit_reason = "Trailing stop triggered";
            }

            // Execute exit if needed
            if(should_exit)
            {
               if(ClosePosition((int)ticket, exit_reason))
                  signals_processed = true;
            }
         }
      }
   }

   return signals_processed;
}

//+------------------------------------------------------------------+
//| Check for bearish reversal signals                               |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckBearishReversal()
{
   double high = iHigh(m_symbol, m_timeframe, 0);
   double low = iLow(m_symbol, m_timeframe, 0);
   double close = iClose(m_symbol, m_timeframe, 0);
   double open = iOpen(m_symbol, m_timeframe, 0);

   // Check for shooting star or bearish engulfing
   double body_size = MathAbs(close - open);
   double candle_range = high - low;
   double upper_wick = high - MathMax(open, close);

   // Shooting star: long upper wick, small body, close near low
   bool is_shooting_star = (upper_wick > body_size * 2) &&
                          (body_size < candle_range * 0.3) &&
                          (close < open);

   // Check previous candle for engulfing pattern
   double prev_close = iClose(m_symbol, m_timeframe, 1);
   double prev_open = iOpen(m_symbol, m_timeframe, 1);
   bool is_bearish_engulfing = (prev_close > prev_open) && (close < open) &&
                               (close < prev_open) && (open > prev_close);

   return is_shooting_star || is_bearish_engulfing;
}

//+------------------------------------------------------------------+
//| Check for bullish reversal signals                               |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckBullishReversal()
{
   double high = iHigh(m_symbol, m_timeframe, 0);
   double low = iLow(m_symbol, m_timeframe, 0);
   double close = iClose(m_symbol, m_timeframe, 0);
   double open = iOpen(m_symbol, m_timeframe, 0);

   // Check for hammer or bullish engulfing
   double body_size = MathAbs(close - open);
   double candle_range = high - low;
   double lower_wick = MathMin(open, close) - low;

   // Hammer: long lower wick, small body, close near high
   bool is_hammer = (lower_wick > body_size * 2) &&
                   (body_size < candle_range * 0.3) &&
                   (close > open);

   // Check previous candle for engulfing pattern
   double prev_close = iClose(m_symbol, m_timeframe, 1);
   double prev_open = iOpen(m_symbol, m_timeframe, 1);
   bool is_bullish_engulfing = (prev_close < prev_open) && (close > open) &&
                               (close > prev_open) && (open < prev_close);

   return is_hammer || is_bullish_engulfing;
}

//+------------------------------------------------------------------+
//| Check and update trailing stop                                   |
//+------------------------------------------------------------------+
bool CTradingEngine::CheckTrailingStop(ulong ticket)
{
   if(!PositionSelectByTicket(ticket))
      return false;

   ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
   double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
   double current_sl = PositionGetDouble(POSITION_SL);
   double current_tp = PositionGetDouble(POSITION_TP);

   // Get ATR for trailing distance
   int atr_handle = iATR(m_symbol, m_timeframe, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001;

   if(CopyBuffer(atr_handle, 0, 0, 1, atr_values) > 0)
      atr = atr_values[0];
   IndicatorRelease(atr_handle);

   double trailing_distance = atr * 1.5; // 1.5 ATR trailing distance
   double new_sl = current_sl;
   bool should_modify = false;

   if(pos_type == POSITION_TYPE_BUY)
   {
      double current_bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
      double potential_sl = current_bid - trailing_distance;

      // Only move SL up (never down)
      if(potential_sl > current_sl && potential_sl > open_price)
      {
         new_sl = potential_sl;
         should_modify = true;
      }
   }
   else // POSITION_TYPE_SELL
   {
      double current_ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
      double potential_sl = current_ask + trailing_distance;

      // Only move SL down (never up)
      if(potential_sl < current_sl && potential_sl < open_price)
      {
         new_sl = potential_sl;
         should_modify = true;
      }
   }

   if(should_modify)
   {
      return ModifyPosition((int)ticket, new_sl, current_tp);
   }

   return false;
}

//+------------------------------------------------------------------+
//| Modify position stop loss and take profit                        |
//+------------------------------------------------------------------+
bool CTradingEngine::ModifyPosition(int ticket, double new_sl, double new_tp)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = new_sl;
   request.tp = new_tp;

   if(OrderSend(request, result))
   {
      if(result.retcode == TRADE_RETCODE_DONE)
      {
         Print("Position ", ticket, " modified: SL=", new_sl, " TP=", new_tp);
         return true;
      }
      else
      {
         Print("Failed to modify position ", ticket, ": ", result.retcode);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Close specific position                                           |
//+------------------------------------------------------------------+
bool CTradingEngine::ClosePosition(int ticket, string reason = "")
{
   if(!PositionSelectByTicket(ticket))
      return false;

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.position = ticket;
   request.symbol = PositionGetString(POSITION_SYMBOL);
   request.volume = PositionGetDouble(POSITION_VOLUME);
   request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                  ORDER_TYPE_SELL : ORDER_TYPE_BUY;
   request.price = (request.type == ORDER_TYPE_SELL) ?
                   SymbolInfoDouble(m_symbol, SYMBOL_BID) :
                   SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   request.magic = m_magic_number;
   request.comment = "Close: " + reason;

   if(OrderSend(request, result))
   {
      if(result.retcode == TRADE_RETCODE_DONE)
      {
         Print("Position ", ticket, " closed: ", reason, " Profit: ",
               PositionGetDouble(POSITION_PROFIT));
         m_current_positions--;
         return true;
      }
      else
      {
         Print("Failed to close position ", ticket, ": ", result.retcode);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CTradingEngine::CloseAllPositions(string reason = "")
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetString(POSITION_SYMBOL) == m_symbol &&
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ClosePosition((int)ticket, reason);
         }
      }
   }

   Print("All positions closed: ", reason);
}

//+------------------------------------------------------------------+
//| Update positions (called periodically)                           |
//+------------------------------------------------------------------+
void CTradingEngine::UpdatePositions()
{
   // Update position count
   GetCurrentPositions();

   // Update daily stats
   UpdateDailyStats();

   // Check exit signals
   CheckExitSignals();

   // Print status periodically
   static datetime last_status_time = 0;
   datetime current_time = TimeCurrent();

   if(current_time - last_status_time >= 3600) // Every hour
   {
      PrintTradingStats();
      last_status_time = current_time;
   }
}

//+------------------------------------------------------------------+
//| Print trading statistics                                          |
//+------------------------------------------------------------------+
void CTradingEngine::PrintTradingStats()
{
   Print("=== CounterAttack Trading Stats ===");
   Print("Current Positions: ", m_current_positions);
   Print("Daily P&L: $", (m_daily_profit - m_daily_loss));
   Print("Daily Profit: $", m_daily_profit);
   Print("Daily Loss: $", m_daily_loss);
   Print("Floating P&L: $", GetFloatingPL());
   Print("Win Rate: ", GetWinRate(), "%");
   Print("Profit Factor: ", GetProfitFactor());
   Print("Trading Allowed: ", (m_trading_allowed ? "YES" : "NO"));
   Print("Account Equity: $", AccountInfoDouble(ACCOUNT_EQUITY));
   Print("Account Balance: $", AccountInfoDouble(ACCOUNT_BALANCE));
   Print("===================================");
}

//+------------------------------------------------------------------+
//| Calculate win rate                                               |
//+------------------------------------------------------------------+
double CTradingEngine::GetWinRate()
{
   int total_trades = 0;
   int winning_trades = 0;

   // Get history for last 30 days
   datetime from_time = TimeCurrent() - (30 * 24 * 3600);
   datetime to_time = TimeCurrent();

   if(HistorySelect(from_time, to_time))
   {
      int total_deals = HistoryDealsTotal();

      for(int i = 0; i < total_deals; i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(ticket > 0)
         {
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == m_magic_number &&
               HistoryDealGetString(ticket, DEAL_SYMBOL) == m_symbol &&
               HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               total_trades++;
               double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);

               if(profit > 0)
                  winning_trades++;
            }
         }
      }
   }

   return (total_trades > 0) ? (double)winning_trades / total_trades * 100.0 : 0.0;
}

//+------------------------------------------------------------------+
//| Calculate profit factor                                           |
//+------------------------------------------------------------------+
double CTradingEngine::GetProfitFactor()
{
   double gross_profit = 0;
   double gross_loss = 0;

   // Get history for last 30 days
   datetime from_time = TimeCurrent() - (30 * 24 * 3600);
   datetime to_time = TimeCurrent();

   if(HistorySelect(from_time, to_time))
   {
      int total_deals = HistoryDealsTotal();

      for(int i = 0; i < total_deals; i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(ticket > 0)
         {
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == m_magic_number &&
               HistoryDealGetString(ticket, DEAL_SYMBOL) == m_symbol &&
               HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);

               if(profit > 0)
                  gross_profit += profit;
               else
                  gross_loss += MathAbs(profit);
            }
         }
      }
   }

   return (gross_loss > 0) ? gross_profit / gross_loss : 0.0;
}

//+------------------------------------------------------------------+
//| Get daily profit/loss                                            |
//+------------------------------------------------------------------+
double CTradingEngine::GetDailyPL()
{
   return m_daily_profit - m_daily_loss;
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool CTradingEngine::IsNewBar()
{
   static datetime last_bar_time = 0;
   datetime current_bar_time = iTime(m_symbol, m_timeframe, 0);

   if(current_bar_time != last_bar_time)
   {
      last_bar_time = current_bar_time;
      return true;
   }

   return false;
}
