# การมีส่วนร่วมในโปรเจค TradingBots

ขอบคุณที่สนใจมีส่วนร่วมในโปรเจค TradingBots! เอกสารนี้จะแนะนำวิธีการมีส่วนร่วมในโปรเจค

## การเริ่มต้น

### ข้อกำหนดเบื้องต้น

- MetaTrader 5
- MetaEditor 5
- ความรู้พื้นฐานเกี่ยวกับ MQL5
- Git (สำหรับการจัดการโค้ด)

### การติดตั้ง

1. Fork โปรเจคนี้
2. Clone โปรเจคไปยังเครื่องของคุณ
3. สร้าง branch ใหม่สำหรับ feature ที่จะพัฒนา

```bash
git clone https://github.com/your-username/TradingBots.git
cd TradingBots
git checkout -b feature/your-feature-name
```

## การพัฒนา

### โครงสร้างโปรเจค

```
TradingBots/
├── src/
│   ├── bots/              # บอทแต่ละตัว
│   ├── core/              # โค้ดหลักที่ใช้ร่วมกัน
│   ├── strategies/        # กลยุทธ์การเทรด
│   └── templates/         # เทมเพลตสำหรับสร้างบอทใหม่
├── config/                # ไฟล์การตั้งค่า
├── docs/                  # เอกสาร
├── tests/                 # การทดสอบ
└── scripts/               # สคริปต์ช่วยเหลือ
```

### การสร้างบอทใหม่

ใช้สคริปต์ `scripts/create_bot.sh` เพื่อสร้างบอทใหม่:

```bash
./scripts/create_bot.sh "BotName" "คำอธิบายบอท" "ชื่อผู้พัฒนา"
```

### แนวทางการเขียนโค้ด

#### การตั้งชื่อ

- **คลาส**: ใช้ PascalCase (เช่น `COrderManager`)
- **ฟังก์ชัน**: ใช้ PascalCase (เช่น `OpenBuyOrder`)
- **ตัวแปร**: ใช้ camelCase (เช่น `stopLoss`)
- **ค่าคงที่**: ใช้ UPPER_CASE (เช่น `MAX_LOT`)

#### การ Comment

- ใช้ภาษาไทยสำหรับ comment หลัก
- ใช้ภาษาอังกฤษสำหรับ technical terms
- Comment ทุกฟังก์ชันและคลาส

#### การจัดการข้อผิดพลาด

```mql5
if(!indicatorManager.Initialize())
{
    Print("ข้อผิดพลาด: ไม่สามารถเริ่มต้น Indicators ได้");
    return INIT_FAILED;
}
```

### การทดสอบ

#### การทดสอบบอท

1. สร้างไฟล์ test ในโฟลเดอร์ `tests/`
2. ทดสอบฟังก์ชันหลักของบอท
3. ทดสอบการจัดการข้อผิดพลาด
4. ทดสอบการทำงานร่วมกับระบบอื่นๆ

#### การ Backtest

1. ทดสอบบอทด้วยข้อมูลย้อนหลัง
2. ตรวจสอบผลการทำงาน
3. บันทึกผลการทดสอบใน `backtests/`

## การส่ง Pull Request

### ก่อนส่ง PR

1. ทดสอบโค้ดให้ครบถ้วน
2. ตรวจสอบว่าโค้ดผ่านการ compile
3. อัปเดตเอกสารที่เกี่ยวข้อง
4. อัปเดต CHANGELOG.md

### การเขียน PR

- ใช้ชื่อที่ชัดเจนและอธิบายการเปลี่ยนแปลง
- อธิบายรายละเอียดการเปลี่ยนแปลง
- ระบุ issue ที่เกี่ยวข้อง (ถ้ามี)
- เพิ่ม screenshot หรือตัวอย่าง (ถ้าจำเป็น)

### การ Review

- ทุก PR ต้องได้รับการ review จากสมาชิกทีม
- แก้ไขข้อเสนอแนะที่ได้รับ
- ตรวจสอบว่าโค้ดตรงตามมาตรฐานของโปรเจค

## การรายงาน Bug

### การรายงาน Bug ที่ดี

1. **หัวข้อ**: อธิบายปัญหาอย่างชัดเจน
2. **รายละเอียด**: อธิบายขั้นตอนการเกิดปัญหา
3. **ผลลัพธ์ที่คาดหวัง**: อธิบายสิ่งที่ควรเกิดขึ้น
4. **ผลลัพธ์ที่เกิดขึ้นจริง**: อธิบายสิ่งที่เกิดขึ้นจริง
5. **ข้อมูลเพิ่มเติม**: เวอร์ชัน, OS, ข้อมูลการทดสอบ

### ตัวอย่างการรายงาน Bug

```
หัวข้อ: GoldBot ไม่สามารถเปิด Order ได้

รายละเอียด:
1. เปิด GoldBot บนชาร์ต XAUUSD
2. ตั้งค่า RiskPercent = 2.0
3. รอสัญญาณการเทรด

ผลลัพธ์ที่คาดหวัง: บอทควรเปิด Order เมื่อมีสัญญาณ

ผลลัพธ์ที่เกิดขึ้นจริง: บอทแสดงข้อผิดพลาด "ไม่สามารถเปิด Order ได้"

ข้อมูลเพิ่มเติม:
- MT5 เวอร์ชัน: 5.0.0.1234
- OS: Windows 10
- Account: Demo
```

## การขอ Feature

### การขอ Feature ที่ดี

1. **หัวข้อ**: อธิบาย feature ที่ต้องการ
2. **เหตุผล**: ทำไมต้องการ feature นี้
3. **รายละเอียด**: อธิบายการทำงานของ feature
4. **ตัวอย่าง**: ตัวอย่างการใช้งาน
5. **ความสำคัญ**: ระดับความสำคัญ (สูง/กลาง/ต่ำ)

## การติดต่อ

- **Issues**: ใช้ GitHub Issues สำหรับรายงาน bug และขอ feature
- **Discussions**: ใช้ GitHub Discussions สำหรับคำถามทั่วไป
- **Email**: <EMAIL> (สำหรับเรื่องสำคัญ)

## ขอบคุณ

ขอบคุณที่ช่วยพัฒนาโปรเจค TradingBots ให้ดีขึ้น!
