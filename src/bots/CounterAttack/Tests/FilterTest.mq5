//+------------------------------------------------------------------+
//|                                                   FilterTest.mq5 |
//|                                  Test Advanced Filter System     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Test"
#property version   "1.00"
#property description "Test script for Advanced Filter System"

#include "../includes/Constants.mqh"
#include "../classes/FilterSystem.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== Advanced Filter System Test ===");
   
   // Initialize filter system
   CFilterSystem* filter = new CFilterSystem(Symbol());
   
   if(!filter)
   {
      Print("Error: Failed to create filter system");
      return;
   }
   
   // Test default parameters
   FilterParams params = CFilterSystem::GetDefaultParams();
   filter.SetFilterParams(params);
   
   Print("Filter parameters loaded:");
   Print("- Volatility Filter: ", params.use_volatility_filter ? "ON" : "OFF");
   Print("- Trend Filter: ", params.use_trend_filter ? "ON" : "OFF");
   Print("- Price Action Filter: ", params.use_price_action_filter ? "ON" : "OFF");
   Print("- ATR Lookback Periods: ", params.atr_lookback_periods);
   Print("- Trend MA Period: ", params.trend_ma_period);
   Print("- Fast MA Period: ", params.trend_ma_fast_period);
   
   // Create test zones
   ZoneInfo test_zones[3];
   
   // Test Zone 1: Resistance zone above current price
   test_zones[0].mid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID) + 50 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[0].upper_price = test_zones[0].mid_price + 25 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[0].lower_price = test_zones[0].mid_price - 25 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[0].type = ZONE_HVN;
   test_zones[0].status = ZONE_FRESH;
   test_zones[0].score = 0;
   test_zones[0].volume = 1000;
   test_zones[0].created_at = TimeCurrent() - 3600; // 1 hour ago
   
   // Test Zone 2: Support zone below current price
   test_zones[1].mid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID) - 50 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[1].upper_price = test_zones[1].mid_price + 25 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[1].lower_price = test_zones[1].mid_price - 25 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[1].type = ZONE_HVN;
   test_zones[1].status = ZONE_FRESH;
   test_zones[1].score = 0;
   test_zones[1].volume = 800;
   test_zones[1].created_at = TimeCurrent() - 7200; // 2 hours ago
   
   // Test Zone 3: LVN breakout zone
   test_zones[2].mid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID) + 20 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[2].upper_price = test_zones[2].mid_price + 15 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[2].lower_price = test_zones[2].mid_price - 15 * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   test_zones[2].type = ZONE_LVN;
   test_zones[2].status = ZONE_FRESH;
   test_zones[2].score = 0;
   test_zones[2].volume = 200;
   test_zones[2].created_at = TimeCurrent() - 1800; // 30 minutes ago
   
   // Test each filter individually
   Print("\n=== Individual Filter Tests ===");
   
   for(int i = 0; i < 3; i++)
   {
      Print("\nTesting Zone ", i + 1, " (", EnumToString(test_zones[i].type), "):");
      Print("Price: ", DoubleToString(test_zones[i].mid_price, 5));
      
      // Test Volatility Filter
      bool vol_pass = filter.PassAdvancedVolatilityFilter(test_zones[i]);
      Print("- Volatility Filter: ", vol_pass ? "PASS" : "FAIL");
      
      // Test Trend Filter
      bool trend_pass = filter.PassAdvancedTrendFilter(test_zones[i]);
      Print("- Trend Filter: ", trend_pass ? "PASS" : "FAIL");
      
      // Test Price Action Filter
      bool pa_pass = filter.PassAdvancedPriceActionFilter(test_zones[i]);
      Print("- Price Action Filter: ", pa_pass ? "PASS" : "FAIL");
      
      // Test Combined Filter
      bool all_pass = filter.PassAllFilters(test_zones[i]);
      Print("- Combined Result: ", all_pass ? "PASS" : "FAIL");
   }
   
   // Test filter array processing
   Print("\n=== Array Filter Test ===");
   
   ZoneInfo filtered_zones[];
   int filtered_count;
   
   bool success = filter.FilterZones(test_zones, 3, filtered_zones, filtered_count);
   
   if(success)
   {
      Print("Filter processing successful!");
      Print("Input zones: 3");
      Print("Output zones: ", filtered_count);
      
      for(int i = 0; i < filtered_count; i++)
      {
         Print("Filtered Zone ", i + 1, ": ", EnumToString(filtered_zones[i].type),
               " at ", DoubleToString(filtered_zones[i].mid_price, 5));
      }
   }
   else
   {
      Print("Filter processing failed - no zones passed all filters");
   }
   
   // Test market condition analysis
   Print("\n=== Market Condition Analysis ===");
   
   filter.UpdateATRValues();
   filter.PrintFilterStatus();
   
   // Clean up
   delete filter;
   
   Print("\n=== Test Complete ===");
}

//+------------------------------------------------------------------+
//| Test helper function to create realistic test data              |
//+------------------------------------------------------------------+
void CreateRealisticTestZones(ZoneInfo &zones[], int count)
{
   ArrayResize(zones, count);
   
   double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double atr = iATR(Symbol(), PERIOD_H1, 14, 1);
   
   for(int i = 0; i < count; i++)
   {
      // Create zones at different distances from current price
      double distance = (i + 1) * atr * 0.5;
      bool above_price = (i % 2 == 0);
      
      zones[i].mid_price = above_price ? current_price + distance : current_price - distance;
      zones[i].upper_price = zones[i].mid_price + atr * 0.25;
      zones[i].lower_price = zones[i].mid_price - atr * 0.25;
      zones[i].type = (i % 3 == 0) ? ZONE_POC : ((i % 3 == 1) ? ZONE_HVN : ZONE_LVN);
      zones[i].status = ZONE_FRESH;
      zones[i].score = 0;
      zones[i].volume = 1000 - (i * 100);
      zones[i].created_at = TimeCurrent() - (i * 1800); // Staggered creation times
   }
}

//+------------------------------------------------------------------+
//| Test specific filter scenarios                                  |
//+------------------------------------------------------------------+
void TestSpecificScenarios()
{
   Print("\n=== Specific Scenario Tests ===");
   
   CFilterSystem* filter = new CFilterSystem(Symbol());
   FilterParams params = CFilterSystem::GetDefaultParams();
   filter.SetFilterParams(params);
   
   // Scenario 1: Low volatility market
   Print("\nScenario 1: Testing low volatility rejection");
   params.min_atr_ratio = 1.5; // Require high volatility
   filter.SetFilterParams(params);
   
   ZoneInfo low_vol_zone;
   low_vol_zone.mid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   low_vol_zone.type = ZONE_HVN;
   low_vol_zone.status = ZONE_FRESH;
   
   bool low_vol_result = filter.PassAdvancedVolatilityFilter(low_vol_zone);
   Print("Low volatility zone result: ", low_vol_result ? "PASS" : "FAIL");
   
   // Scenario 2: Strong trend against zone
   Print("\nScenario 2: Testing trend alignment");
   // This would require more complex setup with historical data
   
   delete filter;
}
