//+------------------------------------------------------------------+
//| PerformanceAnalyzer.mqh - วิเคราะห์ผลการเทรดและสถิติ
//+------------------------------------------------------------------+
#property copyright "MyFirstBot"
#property link      ""
#property version   "1.00"

#include "../includes/Constants.mqh"

//--- คลาสสำหรับการวิเคราะห์ผลการเทรด
class CPerformanceAnalyzer
{
private:
    //--- ข้อมูลสถิติ
    int totalTrades;                    // จำนวนการเทรดทั้งหมด
    int winningTrades;                  // จำนวนการเทรดที่ชนะ
    int losingTrades;                   // จำนวนการเทรดที่แพ้
    int breakEvenTrades;                // จำนวนการเทรดที่เท่าเดิม
    
    double totalProfit;                 // ผลกำไรรวม
    double totalLoss;                   // ความเสียหายรวม
    double maxProfit;                   // กำไรสูงสุดต่อการเทรด
    double maxLoss;                     // ความเสียหายสูงสุดต่อการเทรด
    double maxDrawdown;                 // Drawdown สูงสุด
    double maxDrawdownPercent;          // Drawdown สูงสุด (เปอร์เซ็นต์)
    
    double initialBalance;              // เงินทุนเริ่มต้น
    double currentBalance;              // เงินทุนปัจจุบัน
    double peakBalance;                 // เงินทุนสูงสุด
    
    //--- Arrays สำหรับเก็บข้อมูล
    double tradeResults[];              // ผลลัพธ์การเทรดแต่ละครั้ง
    datetime tradeDates[];              // วันที่ของการเทรด
    double drawdownHistory[];           // ประวัติ Drawdown
    
public:
    //--- Constructor
    CPerformanceAnalyzer();
    
    //--- Destructor
    ~CPerformanceAnalyzer();
    
    //--- ฟังก์ชันการตั้งค่า
    void SetInitialBalance(double balance);
    void SetCurrentBalance(double balance);
    
    //--- ฟังก์ชันการอัพเดทข้อมูล
    void AddTradeResult(double profit, datetime tradeDate);
    void UpdateDrawdown();
    
    //--- ฟังก์ชันการคำนวณสถิติ
    double GetWinRate();
    double GetProfitFactor();
    double GetAverageWin();
    double GetAverageLoss();
    double GetExpectedValue();
    double GetSharpeRatio();
    double GetMaxDrawdown();
    double GetMaxDrawdownPercent();
    
    //--- ฟังก์ชันการแสดงผล
    void PrintStatistics();
    void ExportToCSV(string filename);
    
    //--- ฟังก์ชันการวิเคราะห์
    bool IsStrategyProfitable();
    bool IsRiskAcceptable();
    string GetStrategyRating();
    
private:
    //--- ฟังก์ชันการคำนวณภายใน
    void UpdatePeakBalance();
    void ResizeArrays();
};

//--- Constructor
CPerformanceAnalyzer::CPerformanceAnalyzer()
{
    totalTrades = 0;
    winningTrades = 0;
    losingTrades = 0;
    breakEvenTrades = 0;
    
    totalProfit = 0;
    totalLoss = 0;
    maxProfit = 0;
    maxLoss = 0;
    maxDrawdown = 0;
    maxDrawdownPercent = 0;
    
    initialBalance = 0;
    currentBalance = 0;
    peakBalance = 0;
    
    // กำหนดขนาด arrays
    ArrayResize(tradeResults, 1000);
    ArrayResize(tradeDates, 1000);
    ArrayResize(drawdownHistory, 1000);
}

//--- Destructor
CPerformanceAnalyzer::~CPerformanceAnalyzer()
{
    // ไม่มีอะไรต้องทำ
}

//--- ตั้งค่าเงินทุนเริ่มต้น
void CPerformanceAnalyzer::SetInitialBalance(double balance)
{
    initialBalance = balance;
    currentBalance = balance;
    peakBalance = balance;
}

//--- ตั้งค่าเงินทุนปัจจุบัน
void CPerformanceAnalyzer::SetCurrentBalance(double balance)
{
    currentBalance = balance;
    UpdatePeakBalance();
    UpdateDrawdown();
}

//--- เพิ่มผลลัพธ์การเทรด
void CPerformanceAnalyzer::AddTradeResult(double profit, datetime tradeDate)
{
    if(totalTrades >= ArraySize(tradeResults))
        ResizeArrays();
        
    tradeResults[totalTrades] = profit;
    tradeDates[totalTrades] = tradeDate;
    
    totalTrades++;
    
    if(profit > 0)
    {
        winningTrades++;
        totalProfit += profit;
        if(profit > maxProfit)
            maxProfit = profit;
    }
    else if(profit < 0)
    {
        losingTrades++;
        totalLoss += MathAbs(profit);
        if(MathAbs(profit) > maxLoss)
            maxLoss = MathAbs(profit);
    }
    else
    {
        breakEvenTrades++;
    }
    
    // อัพเดทเงินทุน
    currentBalance += profit;
    UpdatePeakBalance();
    UpdateDrawdown();
}

//--- อัพเดท Drawdown
void CPerformanceAnalyzer::UpdateDrawdown()
{
    if(currentBalance < peakBalance)
    {
        double drawdown = peakBalance - currentBalance;
        double drawdownPercent = (drawdown / peakBalance) * 100;
        
        if(drawdown > maxDrawdown)
        {
            maxDrawdown = drawdown;
            maxDrawdownPercent = drawdownPercent;
        }
        
        // เก็บประวัติ Drawdown
        if(ArraySize(drawdownHistory) > 0)
        {
            static int drawdownIndex = 0;
            if(drawdownIndex < ArraySize(drawdownHistory))
            {
                drawdownHistory[drawdownIndex] = drawdown;
                drawdownIndex++;
            }
        }
    }
}

//--- อัพเดทเงินทุนสูงสุด
void CPerformanceAnalyzer::UpdatePeakBalance()
{
    if(currentBalance > peakBalance)
        peakBalance = currentBalance;
}

//--- คำนวณอัตราการชนะ
double CPerformanceAnalyzer::GetWinRate()
{
    if(totalTrades == 0)
        return 0;
        
    return (double)winningTrades / totalTrades * 100;
}

//--- คำนวณ Profit Factor
double CPerformanceAnalyzer::GetProfitFactor()
{
    if(totalLoss == 0)
        return (totalProfit > 0) ? 999 : 0;
        
    return totalProfit / totalLoss;
}

//--- คำนวณกำไรเฉลี่ย
double CPerformanceAnalyzer::GetAverageWin()
{
    if(winningTrades == 0)
        return 0;
        
    return totalProfit / winningTrades;
}

//--- คำนวณความเสียหายเฉลี่ย
double CPerformanceAnalyzer::GetAverageLoss()
{
    if(losingTrades == 0)
        return 0;
        
    return totalLoss / losingTrades;
}

//--- คำนวณ Expected Value
double CPerformanceAnalyzer::GetExpectedValue()
{
    if(totalTrades == 0)
        return 0;
        
    return (totalProfit - totalLoss) / totalTrades;
}

//--- คำนวณ Sharpe Ratio (แบบง่าย)
double CPerformanceAnalyzer::GetSharpeRatio()
{
    if(totalTrades < 2)
        return 0;
        
    // คำนวณ Standard Deviation
    double mean = (totalProfit - totalLoss) / totalTrades;
    double variance = 0;
    
    for(int i = 0; i < totalTrades; i++)
    {
        variance += MathPow(tradeResults[i] - mean, 2);
    }
    
    variance /= (totalTrades - 1);
    double stdDev = MathSqrt(variance);
    
    if(stdDev == 0)
        return 0;
        
    return mean / stdDev;
}

//--- รับ Drawdown สูงสุด
double CPerformanceAnalyzer::GetMaxDrawdown()
{
    return maxDrawdown;
}

//--- รับ Drawdown สูงสุด (เปอร์เซ็นต์)
double CPerformanceAnalyzer::GetMaxDrawdownPercent()
{
    return maxDrawdownPercent;
}

//--- ตรวจสอบว่ากลยุทธ์มีกำไรหรือไม่
bool CPerformanceAnalyzer::IsStrategyProfitable()
{
    return (totalProfit - totalLoss) > 0;
}

//--- ตรวจสอบว่าความเสี่ยงยอมรับได้หรือไม่
bool CPerformanceAnalyzer::IsRiskAcceptable()
{
    // ตรวจสอบ Drawdown ไม่เกิน 20%
    if(maxDrawdownPercent > 20)
        return false;
        
    // ตรวจสอบ Profit Factor มากกว่า 1.5
    if(GetProfitFactor() < 1.5)
        return false;
        
    return true;
}

//--- ให้คะแนนกลยุทธ์
string CPerformanceAnalyzer::GetStrategyRating()
{
    if(!IsStrategyProfitable())
        return "ไม่แนะนำ (ขาดทุน)";
        
    if(!IsRiskAcceptable())
        return "ต้องปรับปรุง (ความเสี่ยงสูง)";
        
    double winRate = GetWinRate();
    double profitFactor = GetProfitFactor();
    
    if(winRate >= 60 && profitFactor >= 2.0)
        return "ดีมาก (A)";
    else if(winRate >= 50 && profitFactor >= 1.8)
        return "ดี (B)";
    else if(winRate >= 45 && profitFactor >= 1.6)
        return "พอใช้ (C)";
    else
        return "ต้องปรับปรุง (D)";
}

//--- แสดงสถิติ
void CPerformanceAnalyzer::PrintStatistics()
{
    Print("=== สถิติการเทรด ===");
    Print("จำนวนการเทรดทั้งหมด: ", totalTrades);
    Print("การเทรดที่ชนะ: ", winningTrades);
    Print("การเทรดที่แพ้: ", losingTrades);
    Print("การเทรดที่เท่าเดิม: ", breakEvenTrades);
    Print("อัตราการชนะ: ", DoubleToString(GetWinRate(), 2), "%");
    Print("ผลกำไรรวม: ", DoubleToString(totalProfit, 2));
    Print("ความเสียหายรวม: ", DoubleToString(totalLoss, 2));
    Print("กำไรสุทธิ: ", DoubleToString(totalProfit - totalLoss, 2));
    Print("Profit Factor: ", DoubleToString(GetProfitFactor(), 2));
    Print("กำไรเฉลี่ย: ", DoubleToString(GetAverageWin(), 2));
    Print("ความเสียหายเฉลี่ย: ", DoubleToString(GetAverageLoss(), 2));
    Print("Expected Value: ", DoubleToString(GetExpectedValue(), 2));
    Print("Sharpe Ratio: ", DoubleToString(GetSharpeRatio(), 2));
    Print("Drawdown สูงสุด: ", DoubleToString(maxDrawdown, 2), " (", DoubleToString(maxDrawdownPercent, 2), "%)");
    Print("เงินทุนเริ่มต้น: ", DoubleToString(initialBalance, 2));
    Print("เงินทุนปัจจุบัน: ", DoubleToString(currentBalance, 2));
    Print("เงินทุนสูงสุด: ", DoubleToString(peakBalance, 2));
    Print("คะแนนกลยุทธ์: ", GetStrategyRating());
}

//--- Export ข้อมูลเป็น CSV
void CPerformanceAnalyzer::ExportToCSV(string filename)
{
    int fileHandle = FileOpen(filename, FILE_WRITE | FILE_CSV);
    
    if(fileHandle != INVALID_HANDLE)
    {
        // เขียนหัวข้อ
        FileWrite(fileHandle, "Trade #", "Date", "Profit/Loss", "Cumulative Balance");
        
        // เขียนข้อมูล
        double cumulativeBalance = initialBalance;
        for(int i = 0; i < totalTrades; i++)
        {
            cumulativeBalance += tradeResults[i];
            FileWrite(fileHandle, i + 1, TimeToString(tradeDates[i]), tradeResults[i], cumulativeBalance);
        }
        
        FileClose(fileHandle);
        Print("ข้อมูลถูก Export ไปยัง: ", filename);
    }
    else
    {
        Print("ข้อผิดพลาด: ไม่สามารถสร้างไฟล์ CSV ได้");
    }
}

//--- ปรับขนาด Arrays
void CPerformanceAnalyzer::ResizeArrays()
{
    int newSize = ArraySize(tradeResults) * 2;
    ArrayResize(tradeResults, newSize);
    ArrayResize(tradeDates, newSize);
    ArrayResize(drawdownHistory, newSize);
}
