//+------------------------------------------------------------------+
//|                                                    FinalTest.mq5 |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

// Test final compilation with includes
#include "includes/Constants.mqh"

input int TestInput = 10;

int OnInit()
{
   Print("Final compilation test");
   
   // Test structures
   ZoneInfo zone;
   zone.mid_price = 1.0;
   zone.type = ZONE_HVN;
   zone.status = ZONE_FRESH;
   
   FilterParams params;
   params.use_volatility_filter = true;
   params.min_atr_ratio = 1.0;
   
   Print("Zone type: ", zone.type);
   Print("Filter enabled: ", params.use_volatility_filter);
   
   return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
   Print("Test completed successfully");
}

void OnTick()
{
   // Test basic functionality
   static int tick_count = 0;
   tick_count++;
   
   if(tick_count % 100 == 0)
   {
      Print("Tick count: ", tick_count);
   }
}
