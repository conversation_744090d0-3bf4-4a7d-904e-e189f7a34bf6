//+------------------------------------------------------------------+
//|                                               TickDataLoader.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Tick Data Loader Class                                           |
//+------------------------------------------------------------------+
class CTickDataLoader
{
private:
   string            m_symbol;                 // Symbol name
   int               m_chunk_size;             // Chunk size for loading
   TickData          m_tick_buffer[];          // Tick buffer
   int               m_tick_count;             // Current tick count
   
   // Helper methods
   double            CalculatePrice(const MqlTick &tick);
   bool              IsValidTick(const MqlTick &tick);
   bool              IsSpreadNormal(const MqlTick &tick, double max_spread);

public:
   // Constructor/Destructor
                     CTickDataLoader(string symbol = "", int chunk_size = MAX_TICKS_PER_CHUNK);
                    ~CTickDataLoader();
   
   // Main methods
   bool              LoadTicksRange(datetime from_time, datetime to_time);
   bool              LoadTicksSession(const SessionTime &session, datetime date);
   bool              LoadTicksFixedRange(int bars_back);
   
   // Data access
   int               GetTickCount() const { return m_tick_count; }
   bool              GetTick(int index, TickData &tick) const;
   bool              GetTicksArray(TickData &ticks[]) const;
   
   // Utility methods
   void              ClearBuffer();
   bool              SaveToFile(string filename) const;
   bool              LoadFromFile(string filename);
   
   // Statistics
   double            GetMinPrice() const;
   double            GetMaxPrice() const;
   datetime          GetFirstTickTime() const;
   datetime          GetLastTickTime() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CTickDataLoader::CTickDataLoader(string symbol = "", int chunk_size = MAX_TICKS_PER_CHUNK)
{
   m_symbol = (symbol == "") ? ChartSymbol() : symbol;
   m_chunk_size = chunk_size;
   m_tick_count = 0;
   ArrayResize(m_tick_buffer, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CTickDataLoader::~CTickDataLoader()
{
   ArrayFree(m_tick_buffer);
}

//+------------------------------------------------------------------+
//| Calculate price from tick (mid/bid/last priority)               |
//+------------------------------------------------------------------+
double CTickDataLoader::CalculatePrice(const MqlTick &tick)
{
   // Priority: mid price if both bid and ask available
   if(tick.bid > 0 && tick.ask > 0)
      return (tick.bid + tick.ask) / 2.0;
   
   // Fallback to bid if available
   if(tick.bid > 0)
      return tick.bid;
   
   // Last fallback to last price
   if(tick.last > 0)
      return tick.last;
   
   // If nothing available, use ask
   return tick.ask;
}

//+------------------------------------------------------------------+
//| Check if tick is valid                                           |
//+------------------------------------------------------------------+
bool CTickDataLoader::IsValidTick(const MqlTick &tick)
{
   // Check if we have at least one valid price
   if(tick.bid <= 0 && tick.ask <= 0 && tick.last <= 0)
      return false;
   
   // Check for reasonable spread (max 1% for safety)
   if(tick.bid > 0 && tick.ask > 0)
   {
      double spread_ratio = (tick.ask - tick.bid) / tick.bid;
      if(spread_ratio > 0.01) // 1% max spread
         return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if spread is normal                                        |
//+------------------------------------------------------------------+
bool CTickDataLoader::IsSpreadNormal(const MqlTick &tick, double max_spread)
{
   if(tick.bid <= 0 || tick.ask <= 0)
      return true; // Can't check spread
   
   double spread = tick.ask - tick.bid;
   return (spread <= max_spread);
}

//+------------------------------------------------------------------+
//| Load ticks for specific time range                               |
//+------------------------------------------------------------------+
bool CTickDataLoader::LoadTicksRange(datetime from_time, datetime to_time)
{
   ClearBuffer();
   
   MqlTick ticks[];
   int total_copied = 0;
   datetime current_from = from_time;
   
   // Calculate maximum spread for filtering
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double max_spread = 0.001; // Default spread
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      max_spread = atr_values[0] * 0.1; // 10% of ATR
   }
   IndicatorRelease(atr_handle);
   
   Print("Loading ticks from ", TimeToString(from_time), " to ", TimeToString(to_time));
   
   while(current_from < to_time)
   {
      datetime chunk_to = MathMin(current_from + 3600, to_time); // 1 hour chunks
      
      int copied = CopyTicksRange(m_symbol, ticks, COPY_TICKS_ALL,
                                  (ulong)(current_from * 1000), (ulong)(chunk_to * 1000));
      
      if(copied <= 0)
      {
         current_from = chunk_to;
         continue;
      }
      
      // Process ticks in this chunk
      int old_size = ArraySize(m_tick_buffer);
      int valid_ticks = 0;
      
      // Count valid ticks first
      for(int i = 0; i < copied; i++)
      {
         if(IsValidTick(ticks[i]) && IsSpreadNormal(ticks[i], max_spread))
            valid_ticks++;
      }
      
      if(valid_ticks > 0)
      {
         ArrayResize(m_tick_buffer, old_size + valid_ticks);
         
         // Copy valid ticks
         int buffer_index = old_size;
         for(int i = 0; i < copied; i++)
         {
            if(IsValidTick(ticks[i]) && IsSpreadNormal(ticks[i], max_spread))
            {
               m_tick_buffer[buffer_index].time = (datetime)(ticks[i].time_msc / 1000);
               m_tick_buffer[buffer_index].price = CalculatePrice(ticks[i]);
               m_tick_buffer[buffer_index].bid = ticks[i].bid;
               m_tick_buffer[buffer_index].ask = ticks[i].ask;
               m_tick_buffer[buffer_index].last = ticks[i].last;
               m_tick_buffer[buffer_index].volume = (ulong)ticks[i].volume_real;
               m_tick_buffer[buffer_index].flags = ticks[i].flags;
               buffer_index++;
            }
         }
         
         total_copied += valid_ticks;
      }
      
      current_from = chunk_to;
      
      // Progress update
      if(total_copied % 10000 == 0)
         Print("Loaded ", total_copied, " ticks so far...");
   }
   
   m_tick_count = ArraySize(m_tick_buffer);
   
   Print("Tick loading completed. Total ticks: ", m_tick_count);
   
   if(m_tick_count < MIN_TICKS_REQUIRED)
   {
      Print("Warning: Only ", m_tick_count, " ticks loaded. Minimum required: ", MIN_TICKS_REQUIRED);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Load ticks for session                                           |
//+------------------------------------------------------------------+
bool CTickDataLoader::LoadTicksSession(const SessionTime &session, datetime date)
{
   // Calculate session start and end times
   datetime session_start = date + session.start_hour * 3600 + session.start_minute * 60;
   datetime session_end = date + session.end_hour * 3600 + session.end_minute * 60;
   
   // Handle overnight sessions
   if(session_end <= session_start)
      session_end += 86400; // Add one day
   
   return LoadTicksRange(session_start, session_end);
}

//+------------------------------------------------------------------+
//| Get tick by index                                                |
//+------------------------------------------------------------------+
bool CTickDataLoader::GetTick(int index, TickData &tick) const
{
   if(index < 0 || index >= m_tick_count)
      return false;
   
   tick = m_tick_buffer[index];
   return true;
}

//+------------------------------------------------------------------+
//| Get all ticks as array                                           |
//+------------------------------------------------------------------+
bool CTickDataLoader::GetTicksArray(TickData &ticks[]) const
{
   if(m_tick_count <= 0)
      return false;
   
   ArrayResize(ticks, m_tick_count);
   ArrayCopy(ticks, m_tick_buffer);
   return true;
}

//+------------------------------------------------------------------+
//| Clear tick buffer                                                |
//+------------------------------------------------------------------+
void CTickDataLoader::ClearBuffer()
{
   ArrayResize(m_tick_buffer, 0);
   m_tick_count = 0;
}

//+------------------------------------------------------------------+
//| Get minimum price                                                |
//+------------------------------------------------------------------+
double CTickDataLoader::GetMinPrice() const
{
   if(m_tick_count <= 0)
      return 0;
   
   double min_price = m_tick_buffer[0].price;
   for(int i = 1; i < m_tick_count; i++)
   {
      if(m_tick_buffer[i].price < min_price)
         min_price = m_tick_buffer[i].price;
   }
   
   return min_price;
}

//+------------------------------------------------------------------+
//| Get maximum price                                                |
//+------------------------------------------------------------------+
double CTickDataLoader::GetMaxPrice() const
{
   if(m_tick_count <= 0)
      return 0;

   double max_price = m_tick_buffer[0].price;
   for(int i = 1; i < m_tick_count; i++)
   {
      if(m_tick_buffer[i].price > max_price)
         max_price = m_tick_buffer[i].price;
   }

   return max_price;
}

//+------------------------------------------------------------------+
//| Get first tick time                                              |
//+------------------------------------------------------------------+
datetime CTickDataLoader::GetFirstTickTime() const
{
   if(m_tick_count <= 0)
      return 0;

   return m_tick_buffer[0].time;
}

//+------------------------------------------------------------------+
//| Get last tick time                                               |
//+------------------------------------------------------------------+
datetime CTickDataLoader::GetLastTickTime() const
{
   if(m_tick_count <= 0)
      return 0;

   return m_tick_buffer[m_tick_count - 1].time;
}

//+------------------------------------------------------------------+
//| Load ticks for fixed range (bars back)                          |
//+------------------------------------------------------------------+
bool CTickDataLoader::LoadTicksFixedRange(int bars_back)
{
   if(bars_back <= 0)
      return false;

   datetime end_time = iTime(m_symbol, PERIOD_H1, 1); // Previous completed bar
   datetime start_time = iTime(m_symbol, PERIOD_H1, bars_back);

   if(start_time <= 0 || end_time <= 0)
      return false;

   return LoadTicksRange(start_time, end_time);
}
