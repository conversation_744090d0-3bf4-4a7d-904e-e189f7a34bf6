# TradingBots Global Configuration File
# ไฟล์การตั้งค่าทั่วไปสำหรับโปรเจค

# === Project Information ===
PROJECT_NAME=TradingBots
PROJECT_VERSION=1.00
PROJECT_DESCRIPTION=แพลตฟอร์มสำหรับการพัฒนาและจัดการ Expert Advisors
PROJECT_AUTHOR=TradingBots Team
PROJECT_LICENSE=MIT

# === Development Settings ===
DEBUG_MODE=true
LOG_LEVEL=INFO
SAVE_LOGS=true
LOG_DIR=logs

# === Team Collaboration ===
CODE_REVIEW_REQUIRED=true
TESTING_REQUIRED=true
DOCUMENTATION_REQUIRED=true
VERSION_CONTROL=git

# === Common Trading Settings ===
DEFAULT_MAGIC_NUMBER=100000
DEFAULT_COMMENT_PREFIX=TB
MAX_SPREAD_PIPS=10
MIN_LOT_SIZE=0.01
MAX_LOT_SIZE=100.0

# === Risk Management Defaults ===
DEFAULT_RISK_PERCENT=1.0
DEFAULT_STOP_LOSS_PIPS=50
DEFAULT_TAKE_PROFIT_PIPS=100
MAX_DAILY_LOSS_PERCENT=5.0
MAX_WEEKLY_LOSS_PERCENT=15.0

# === Performance Settings ===
ENABLE_PERFORMANCE_TRACKING=true
SAVE_TRADE_HISTORY=true
ANALYZE_DRAWDOWN=true
CALCULATE_SHARPE_RATIO=true

# === File Paths ===
BOTS_DIR=src/bots
CORE_DIR=src/core
CONFIG_DIR=config
DOCS_DIR=docs
BACKTEST_DIR=backtests
TESTS_DIR=tests

# === Testing Settings ===
ENABLE_UNIT_TESTS=true
ENABLE_INTEGRATION_TESTS=true
ENABLE_BACKTEST_TESTS=true
TEST_TIMEFRAMES=M1,M5,M15,M30,H1,H4,D1

# === Documentation ===
REQUIRE_README=true
REQUIRE_CHANGELOG=true
REQUIRE_API_DOCS=true
DOCS_FORMAT=markdown
