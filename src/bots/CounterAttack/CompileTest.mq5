//+------------------------------------------------------------------+
//|                                                  CompileTest.mq5 |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

// Test basic compilation
#include "includes/Constants.mqh"
#include "classes/VolumeProfileCalculator.mqh"
#include "classes/ZoneManager.mqh"

input int TestParam = 10;

int OnInit()
{
   Print("Compile test successful");
   
   // Test enum
   ENUM_RANGE_MODE range_mode = RANGE_SESSION;
   
   // Test struct
   ZoneInfo zone_info;
   zone_info.mid_price = 1.0;
   zone_info.type = ZONE_HVN;
   zone_info.status = ZONE_FRESH;
   
   // Test VolumeProfileCalculator
   CVolumeProfileCalculator vpc;
   vpc.SetSmoothWindow(5);
   vpc.SetHVNPercentile(80.0);
   vpc.SetLVNPercentile(20.0);
   
   // Test arrays for function calls
   int indices[100];
   int count = 0;
   
   // These should compile without errors now
   vpc.FindHVNNodes(indices, count);
   vpc.FindLVNNodes(indices, count);
   vpc.FindLocalMaxima(indices, count);
   vpc.FindLocalMinima(indices, count);
   vpc.MergeNearbyNodes(indices, count, 0.001);
   
   // Test ZoneManager
   CZoneManager zm;
   zm.SetZoneWidthMultiplier(0.25);
   zm.SetTouchTolerance(0.25);
   zm.SetMaxZones(12);
   
   // Test arrays for ZoneManager
   int hvn_indices[10];
   int lvn_indices[10];
   int hvn_count = 0;
   int lvn_count = 0;
   VolumeProfileBin bins[100];
   int bin_count = 0;
   
   // This should compile without errors now
   zm.CreateZonesFromNodes(0, hvn_indices, hvn_count, lvn_indices, lvn_count, bins, bin_count, TimeCurrent());
   
   Print("Mode: ", range_mode);
   Print("Zone price: ", zone_info.mid_price);
   Print("VPC test successful");
   Print("ZoneManager test successful");
   
   return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
   Print("Test completed");
}

void OnTick()
{
   // Empty
}
