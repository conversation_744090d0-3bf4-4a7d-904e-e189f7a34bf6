//+------------------------------------------------------------------+
//| IndicatorManager.mqh - การจัดการ Indicators
//+------------------------------------------------------------------+
#property copyright "MyFirstBot"
#property link      ""
#property version   "1.00"

#include "Constants.mqh"

//--- คลาสสำหรับการจัดการ Indicators
class CIndicatorManager
{
private:
    int rsiHandle;                    // Handle ของ RSI
    int macdHandle;                   // Handle ของ MACD
    int bollingerHandle;              // Handle ของ Bollinger Bands
    
    double rsiValues[];               // ค่า RSI
    double macdValues[];              // ค่า MACD
    double macdSignalValues[];        // ค่า MACD Signal
    double bollingerUpper[];          // ค่า Bollinger Upper
    double bollingerLower[];          // ค่า Bollinger Lower
    double bollingerMiddle[];         // ค่า Bollinger Middle
    
    bool indicatorsInitialized;        // สถานะการเริ่มต้น Indicators
    
public:
    //--- Constructor
    CIndicatorManager();
    
    //--- Destructor
    ~CIndicatorManager();
    
    //--- ฟังก์ชันการเริ่มต้น
    bool Initialize();
    void Deinitialize();
    
    //--- ฟังก์ชันการอัพเดทข้อมูล
    bool UpdateData();
    
    //--- ฟังก์ชันการตรวจสอบสัญญาณ
    bool CheckBuySignal();
    bool CheckSellSignal();
    
    //--- ฟังก์ชันการตรวจสอบ Indicators แต่ละตัว
    bool IsRSIBuySignal();
    bool IsRSISellSignal();
    bool IsMACDBuySignal();
    bool IsMACDSellSignal();
    bool IsBollingerBuySignal();
    bool IsBollingerSellSignal();
    
    //--- ฟังก์ชันการตรวจสอบคุณภาพสัญญาณ
    double GetSignalStrength();
    
    //--- ฟังก์ชันการรับค่าจาก Indicators
    double GetRSI(int period, int shift);
    double GetMACD(int fastEMA, int slowEMA, int signalEMA, int shift);
    double GetMACDSignal(int fastEMA, int slowEMA, int signalEMA, int shift);
    double GetBollingerUpper(int period, double deviation, int shift);
    double GetBollingerLower(int period, double deviation, int shift);
    
private:
    //--- ฟังก์ชันการเริ่มต้น Indicators แต่ละตัว
    bool InitializeRSI();
    bool InitializeMACD();
    bool InitializeBollinger();
    
    //--- ฟังก์ชันการอัพเดทข้อมูล Indicators แต่ละตัว
    bool UpdateRSIData();
    bool UpdateMACDData();
    bool UpdateBollingerData();
};

//--- Constructor
CIndicatorManager::CIndicatorManager()
{
    rsiHandle = INVALID_HANDLE;
    macdHandle = INVALID_HANDLE;
    bollingerHandle = INVALID_HANDLE;
    indicatorsInitialized = false;
    
    // กำหนดขนาดของ arrays
    ArraySetAsSeries(rsiValues, true);
    ArraySetAsSeries(macdValues, true);
    ArraySetAsSeries(macdSignalValues, true);
    ArraySetAsSeries(bollingerUpper, true);
    ArraySetAsSeries(bollingerLower, true);
    ArraySetAsSeries(bollingerMiddle, true);
}

//--- Destructor
CIndicatorManager::~CIndicatorManager()
{
    Deinitialize();
}

//--- เริ่มต้น Indicators ทั้งหมด
bool CIndicatorManager::Initialize()
{
    if(!InitializeRSI())
        return false;
        
    if(!InitializeMACD())
        return false;
        
    if(!InitializeBollinger())
        return false;
        
    indicatorsInitialized = true;
    return true;
}

//--- ปิด Indicators ทั้งหมด
void CIndicatorManager::Deinitialize()
{
    if(rsiHandle != INVALID_HANDLE)
    {
        IndicatorRelease(rsiHandle);
        rsiHandle = INVALID_HANDLE;
    }
    
    if(macdHandle != INVALID_HANDLE)
    {
        IndicatorRelease(macdHandle);
        macdHandle = INVALID_HANDLE;
    }
    
    if(bollingerHandle != INVALID_HANDLE)
    {
        IndicatorRelease(bollingerHandle);
        bollingerHandle = INVALID_HANDLE;
    }
    
    indicatorsInitialized = false;
}

//--- อัพเดทข้อมูล Indicators ทั้งหมด
bool CIndicatorManager::UpdateData()
{
    if(!indicatorsInitialized)
        return false;
        
    if(!UpdateRSIData())
        return false;
        
    if(!UpdateMACDData())
        return false;
        
    if(!UpdateBollingerData())
        return false;
        
    return true;
}

//--- ตรวจสอบสัญญาณ Buy
bool CIndicatorManager::CheckBuySignal()
{
    // ต้องผ่านอย่างน้อย 2 ใน 3 เงื่อนไข
    int buySignals = 0;
    if(IsRSIBuySignal()) buySignals++;
    if(IsMACDBuySignal()) buySignals++;
    if(IsBollingerBuySignal()) buySignals++;
    
    return buySignals >= 2;
}

//--- ตรวจสอบสัญญาณ Sell
bool CIndicatorManager::CheckSellSignal()
{
    // ต้องผ่านอย่างน้อย 2 ใน 3 เงื่อนไข
    int sellSignals = 0;
    if(IsRSISellSignal()) sellSignals++;
    if(IsMACDSellSignal()) sellSignals++;
    if(IsBollingerSellSignal()) sellSignals++;
    
    return sellSignals >= 2;
}

//--- ตรวจสอบสัญญาณ RSI Buy
bool CIndicatorManager::IsRSIBuySignal()
{
    if(ArraySize(rsiValues) < 2)
        return false;
        
    // RSI ขึ้นจาก oversold หรือกำลังเพิ่มขึ้นจากระดับต่ำ
    return (rsiValues[1] < RSI_OVERSOLD && rsiValues[0] > RSI_OVERSOLD) ||
           (rsiValues[0] > rsiValues[1] && rsiValues[0] < 60) ||
           (rsiValues[0] < 40 && rsiValues[0] > rsiValues[1]);
}

//--- ตรวจสอบสัญญาณ RSI Sell
bool CIndicatorManager::IsRSISellSignal()
{
    if(ArraySize(rsiValues) < 2)
        return false;
        
    // RSI ลงจาก overbought หรือกำลังลดลงจากระดับสูง
    return (rsiValues[1] > RSI_OVERBOUGHT && rsiValues[0] < RSI_OVERBOUGHT) ||
           (rsiValues[0] < rsiValues[1] && rsiValues[0] > 40) ||
           (rsiValues[0] > 60 && rsiValues[0] < rsiValues[1]);
}

//--- ตรวจสอบสัญญาณ MACD Buy
bool CIndicatorManager::IsMACDBuySignal()
{
    if(ArraySize(macdValues) < 2 || ArraySize(macdSignalValues) < 2)
        return false;
        
    // MACD ขึ้นเหนือ Signal หรือกำลังเพิ่มขึ้น
    return (macdValues[0] > macdSignalValues[0]) ||
           (macdValues[0] > macdValues[1] && macdValues[0] > macdSignalValues[0]);
}

//--- ตรวจสอบสัญญาณ MACD Sell
bool CIndicatorManager::IsMACDSellSignal()
{
    if(ArraySize(macdValues) < 2 || ArraySize(macdSignalValues) < 2)
        return false;
        
    // MACD ลงใต้ Signal หรือกำลังลดลง
    return (macdValues[0] < macdSignalValues[0]) ||
           (macdValues[0] < macdValues[1] && macdValues[0] < macdSignalValues[0]);
}

//--- ตรวจสอบสัญญาณ Bollinger Bands Buy
bool CIndicatorManager::IsBollingerBuySignal()
{
    if(ArraySize(bollingerLower) < 1)
        return false;
        
    double close = iClose(Symbol(), Period(), 0);
    
    // ราคาปิดใกล้หรือต่ำกว่า Lower Band (oversold) - เพิ่มความยืดหยุ่น
    return (close <= bollingerLower[0] * 1.005);  // เพิ่มจาก 1.001 เป็น 1.005
}

//--- ตรวจสอบสัญญาณ Bollinger Bands Sell
bool CIndicatorManager::IsBollingerSellSignal()
{
    if(ArraySize(bollingerUpper) < 1)
        return false;
        
    double close = iClose(Symbol(), Period(), 0);
    
    // ราคาปิดใกล้หรือสูงกว่า Upper Band (overbought) - เพิ่มความยืดหยุ่น
    return (close >= bollingerUpper[0] * 0.995);  // เพิ่มจาก 0.999 เป็น 0.995
}

//--- คำนวณความแข็งแกร่งของสัญญาณ
double CIndicatorManager::GetSignalStrength()
{
    double strength = 0;
    
    if(IsRSIBuySignal() || IsRSISellSignal())
        strength += 33.33;
        
    if(IsMACDBuySignal() || IsMACDSellSignal())
        strength += 33.33;
        
    if(IsBollingerBuySignal() || IsBollingerSellSignal())
        strength += 33.34;
        
    return strength;
}

//--- ฟังก์ชันการรับค่าจาก Indicators
double CIndicatorManager::GetRSI(int period, int shift)
{
    if(rsiHandle == INVALID_HANDLE)
        return 0;
        
    if(CopyBuffer(rsiHandle, 0, shift, 1, rsiValues) < 1)
        return 0;
        
    return rsiValues[0];
}

double CIndicatorManager::GetMACD(int fastEMA, int slowEMA, int signalEMA, int shift)
{
    if(macdHandle == INVALID_HANDLE)
        return 0;
        
    if(CopyBuffer(macdHandle, 0, shift, 1, macdValues) < 1)
        return 0;
        
    return macdValues[0];
}

double CIndicatorManager::GetMACDSignal(int fastEMA, int slowEMA, int signalEMA, int shift)
{
    if(macdHandle == INVALID_HANDLE)
        return 0;
        
    if(CopyBuffer(macdHandle, 1, shift, 1, macdSignalValues) < 1)
        return 0;
        
    return macdSignalValues[0];
}

double CIndicatorManager::GetBollingerUpper(int period, double deviation, int shift)
{
    if(bollingerHandle == INVALID_HANDLE)
        return 0;
        
    if(CopyBuffer(bollingerHandle, 1, shift, 1, bollingerUpper) < 1)
        return 0;
        
    return bollingerUpper[0];
}

double CIndicatorManager::GetBollingerLower(int period, double deviation, int shift)
{
    if(bollingerHandle == INVALID_HANDLE)
        return 0;
        
    if(CopyBuffer(bollingerHandle, 2, shift, 1, bollingerLower) < 1)
        return 0;
        
    return bollingerLower[0];
}

//--- เริ่มต้น RSI
bool CIndicatorManager::InitializeRSI()
{
    rsiHandle = iRSI(Symbol(), Period(), RSI_PERIOD, PRICE_CLOSE);
    return (rsiHandle != INVALID_HANDLE);
}

//--- เริ่มต้น MACD
bool CIndicatorManager::InitializeMACD()
{
    macdHandle = iMACD(Symbol(), Period(), MACD_FAST_EMA, MACD_SLOW_EMA, 
                       MACD_SIGNAL_EMA, PRICE_CLOSE);
    return (macdHandle != INVALID_HANDLE);
}

//--- เริ่มต้น Bollinger Bands
bool CIndicatorManager::InitializeBollinger()
{
    bollingerHandle = iBands(Symbol(), Period(), BOLLINGER_PERIOD, 0, 
                             BOLLINGER_DEVIATION, PRICE_CLOSE);
    return (bollingerHandle != INVALID_HANDLE);
}

//--- อัพเดทข้อมูล RSI
bool CIndicatorManager::UpdateRSIData()
{
    if(rsiHandle == INVALID_HANDLE)
        return false;
        
    if(CopyBuffer(rsiHandle, 0, 0, 3, rsiValues) < 2)
        return false;
        
    return true;
}

//--- อัพเดทข้อมูล MACD
bool CIndicatorManager::UpdateMACDData()
{
    if(macdHandle == INVALID_HANDLE)
        return false;
        
    if(CopyBuffer(macdHandle, 0, 0, 3, macdValues) < 2)
        return false;
        
    if(CopyBuffer(macdHandle, 1, 0, 3, macdSignalValues) < 2)
        return false;
        
    return true;
}

//--- อัพเดทข้อมูล Bollinger Bands
bool CIndicatorManager::UpdateBollingerData()
{
    if(bollingerHandle == INVALID_HANDLE)
        return false;
        
    if(CopyBuffer(bollingerHandle, 1, 0, 3, bollingerUpper) < 1)
        return false;
        
    if(CopyBuffer(bollingerHandle, 2, 0, 3, bollingerLower) < 1)
        return false;
        
    if(CopyBuffer(bollingerHandle, 0, 0, 3, bollingerMiddle) < 1)
        return false;
        
    return true;
}
