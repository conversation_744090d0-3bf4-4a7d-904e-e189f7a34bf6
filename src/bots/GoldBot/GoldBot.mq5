//+------------------------------------------------------------------+
//| GoldBot.mq5 - EA สำหรับเทรดทองคำ XAUUSD.
//| ใช้ไฟล์ .conf สำหรับการตั้งค่า (แก้ไขเมื่อ 12 สิงหาคม 2025)
//+------------------------------------------------------------------+
#property copyright "TradingBots Team"
#property link      "https://github.com/tradingbots"
#property version   "2.00"
#property description "EA สำหรับเทรดทองคำ XAUUSD. โดยใช้ 3 Indicators"
#property description "RSI, MACD, และ Bollinger Bands"
#property description "การตั้งค่าอ่านจากไฟล์ config/bots/GoldBot.conf"

//--- Include files
#include "../../core/includes/Constants.mqh"
#include "../../core/includes/OrderManager.mqh"
#include "../../core/includes/IndicatorManager.mqh"
#include "../../core/classes/PerformanceAnalyzer.mqh"

//--- Input Parameters (เฉพาะส่วนที่จำเป็น)
input group "=== Trading Settings ==="
input bool EnableTrading = true;          // เปิดใช้งานการเทรด
input int MagicNumber = 123456;           // Magic Number
input string TradeComment = "GoldBot";    // ข้อความใน Order

input group "=== Configuration File ==="
input string ConfigFile = "config/bots/GoldBot.conf";  // ไฟล์การตั้งค่า

//--- Global Variables
COrderManager orderManager;               // ตัวจัดการ Order
CIndicatorManager indicatorManager;       // ตัวจัดการ Indicators
CPerformanceAnalyzer performanceAnalyzer; // ตัววิเคราะห์ประสิทธิภาพ

//--- ตัวแปรสำหรับการตั้งค่าจากไฟล์ .conf
double configRiskPercent;                 // ความเสี่ยงต่อการเทรด (เปอร์เซ็นต์)
double configStopLossPips;                // Stop Loss (pips)
double configTakeProfitPips;              // Take Profit (pips)
int configRSIPeriod;                      // คาบเวลาของ RSI
int configMACDFastEMA;                    // Fast EMA สำหรับ MACD
int configMACDSlowEMA;                    // Slow EMA สำหรับ MACD
int configMACDSignalEMA;                  // Signal EMA สำหรับ MACD
int configBollingerPeriod;                // คาบเวลาของ Bollinger Bands
double configBollingerDeviation;          // ค่า Standard Deviation

datetime lastBarTime;                     // เวลาของแท่งเทียนล่าสุด
bool isNewBar;                           // ตรวจสอบแท่งเทียนใหม่

//--- ตัวแปรสำหรับติดตาม Order
ulong currentOrderTicket;                 // Ticket ของ Order ปัจจุบัน
double currentOrderOpenPrice;             // ราคาที่เปิด Order
int currentOrderType;                     // ประเภทของ Order (1=Buy, -1=Sell)
bool hasOpenOrder;                        // มี Order เปิดอยู่หรือไม่

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== GoldBot Initialization ===");
    
    // อ่านการตั้งค่าจากไฟล์ .conf
    if(!LoadConfiguration())
    {
        Print("ข้อผิดพลาด: ไม่สามารถอ่านไฟล์การตั้งค่าได้");
        return INIT_FAILED;
    }
    
    // ตรวจสอบสัญลักษณ์
    if(Symbol() != "XAUUSD.")
    {
        Print("คำเตือน: EA นี้ถูกออกแบบมาสำหรับ XAUUSD. เท่านั้น");
        Print("สัญลักษณ์ปัจจุบัน: ", Symbol());
    }
    
    // เริ่มต้น Order Manager
    orderManager.SetSymbol(Symbol());
    
    // ใช้ค่าจากไฟล์ .conf แทน constants
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if(Symbol() == "XAUUSD.")
    {
        point = 0.1; // 1 pip = 0.1 สำหรับ XAUUSD
    }
    
    // ใช้ค่าจากไฟล์ .conf
    orderManager.SetStopLoss(configStopLossPips * point);
    orderManager.SetTakeProfit(configTakeProfitPips * point);
    
    // คำนวณขนาด Lot ตามความเสี่ยง
    double lotSize = orderManager.CalculateLotSize(configRiskPercent, configStopLossPips);
    orderManager.SetLotSize(lotSize);
    
    Print("=== การตั้งค่าจากไฟล์ .conf ===");
    Print("Risk Percent: ", configRiskPercent, "%");
    Print("Stop Loss: ", configStopLossPips, " pips (", configStopLossPips * point, " ราคา)");
    Print("Take Profit: ", configTakeProfitPips, " pips (", configTakeProfitPips * point, " ราคา)");
    Print("Point ที่ใช้: ", point);
    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);
    
    // เริ่มต้น Indicator Manager
    if(!indicatorManager.Initialize())
    {
        Print("ข้อผิดพลาด: ไม่สามารถเริ่มต้น Indicators ได้");
        return INIT_FAILED;
    }
    
    // เริ่มต้น Performance Analyzer
    performanceAnalyzer.SetInitialBalance(AccountInfoDouble(ACCOUNT_BALANCE));
    
    Print("Indicators เริ่มต้นสำเร็จ");
    
    // ตั้งค่าเวลาเริ่มต้น
    lastBarTime = iTime(Symbol(), Period(), 0);
    
    // เริ่มต้นตัวแปรติดตาม Order
    currentOrderTicket = 0;
    currentOrderOpenPrice = 0;
    currentOrderType = 0;
    hasOpenOrder = false;
    
    Print("=== GoldBot พร้อมใช้งาน ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== GoldBot Deinitialization ===");
    
    // ปิด Indicators
    indicatorManager.Deinitialize();
    
    // ปิด Order ทั้งหมด
    orderManager.CloseAllOrders();
    
    // บันทึกสถิติประสิทธิภาพ
    performanceAnalyzer.PrintStatistics();
    
    Print("GoldBot ถูกปิดแล้ว");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // ตรวจสอบแท่งเทียนใหม่
    if(!IsNewBar())
        return;
    
    // ตรวจสอบการเปิดใช้งานการเทรด
    if(!EnableTrading)
        return;
    
    // ตรวจสอบ Order ที่เปิดอยู่
    if(hasOpenOrder)
    {
        // ตรวจสอบว่า Order ยังเปิดอยู่หรือไม่
        if(!PositionSelectByTicket(currentOrderTicket))
        {
            // Order ถูกปิดแล้ว ให้คำนวณผลกำไร/ขาดทุน
            CalculateTradeResult();
            hasOpenOrder = false;
            currentOrderTicket = 0;
        }
        return;
    }
    
    // วิเคราะห์สัญญาณการเทรด
    int signal = AnalyzeTradingSignal();
    
    // ดำเนินการตามสัญญาณ
    if(signal == 1) // สัญญาณ Buy
    {
        if(OpenBuyOrder())
        {
            Print("เปิด Order Buy สำเร็จ");
        }
    }
    else if(signal == -1) // สัญญาณ Sell
    {
        if(OpenSellOrder())
        {
            Print("เปิด Order Sell สำเร็จ");
        }
    }
}

//+------------------------------------------------------------------+
//| ตรวจสอบแท่งเทียนใหม่                                            |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        isNewBar = true;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| วิเคราะห์สัญญาณการเทรด                                          |
//+------------------------------------------------------------------+
int AnalyzeTradingSignal()
{
    // อัพเดทข้อมูล Indicators
    if(!indicatorManager.UpdateData())
        return 0;
    
    // ตรวจสอบสัญญาณการเทรด
    if(indicatorManager.CheckBuySignal())
        return 1; // สัญญาณ Buy
    
    if(indicatorManager.CheckSellSignal())
        return -1; // สัญญาณ Sell
    
    return 0; // ไม่มีสัญญาณ
}

//+------------------------------------------------------------------+
//| เปิด Order Buy                                                    |
//+------------------------------------------------------------------+
bool OpenBuyOrder()
{
    if(orderManager.OpenBuyOrder())
    {
        // บันทึกข้อมูล Order
        currentOrderTicket = orderManager.GetLastOrderTicket();
        currentOrderOpenPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        currentOrderType = 1;
        hasOpenOrder = true;
        
        Print("เปิด Order Buy สำเร็จ - Ticket: ", currentOrderTicket, " ราคา: ", currentOrderOpenPrice);
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| เปิด Order Sell                                                   |
//+------------------------------------------------------------------+
bool OpenSellOrder()
{
    if(orderManager.OpenSellOrder())
    {
        // บันทึกข้อมูล Order
        currentOrderTicket = orderManager.GetLastOrderTicket();
        currentOrderOpenPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        currentOrderType = -1;
        hasOpenOrder = true;
        
        Print("เปิด Order Sell สำเร็จ - Ticket: ", currentOrderTicket, " ราคา: ", currentOrderOpenPrice);
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| คำนวณผลการเทรด                                                   |
//+------------------------------------------------------------------+
void CalculateTradeResult()
{
    double currentPrice = 0;
    double profit = 0;
    
    // ใช้ SymbolInfoDouble แทน _Point เพื่อความแม่นยำสำหรับ XAUUSD
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    
    // ตรวจสอบว่าเป็น XAUUSD หรือไม่
    if(Symbol() == "XAUUSD.")
    {
        // สำหรับ XAUUSD ใช้ค่าที่เหมาะสม
        point = 0.1; // 1 pip = 0.1 สำหรับ XAUUSD
    }
    
    if(currentOrderType == 1) // Buy Order
    {
        currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        profit = (currentPrice - currentOrderOpenPrice) / point;
    }
    else if(currentOrderType == -1) // Sell Order
    {
        currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        profit = (currentOrderOpenPrice - currentPrice) / point;
    }
    
    // ปัดเศษผลกำไรให้เป็นทศนิยม 2 ตำแหน่ง
    profit = NormalizeDouble(profit, 2);
    
    // บันทึกผลการเทรด
    performanceAnalyzer.AddTradeResult(profit, TimeCurrent());
    
    Print("Order ปิดแล้ว - Ticket: ", currentOrderTicket, " ผลกำไร: ", profit, " pips");
    Print("ราคาเปิด: ", currentOrderOpenPrice, " ราคาปัจจุบัน: ", currentPrice, " Point: ", point);
}

//+------------------------------------------------------------------+
//| อ่านการตั้งค่าจากไฟล์ .conf                                      |
//+------------------------------------------------------------------+
bool LoadConfiguration()
{
    Print("กำลังอ่านไฟล์การตั้งค่า: ", ConfigFile);
    
    // ตั้งค่าเริ่มต้น (fallback values)
    configRiskPercent = 1.5;
    configStopLossPips = 35;
    configTakeProfitPips = 70;
    configRSIPeriod = 14;
    configMACDFastEMA = 12;
    configMACDSlowEMA = 26;
    configMACDSignalEMA = 9;
    configBollingerPeriod = 20;
    configBollingerDeviation = 2.0;
    
    // อ่านไฟล์ .conf
    int fileHandle = FileOpen(ConfigFile, FILE_READ | FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        string line;
        while(!FileIsEnding(fileHandle))
        {
            line = FileReadString(fileHandle);
            // ลบช่องว่างด้านซ้ายและขวาออกจาก string
            StringTrimLeft(line);
            StringTrimRight(line);
            
            // ข้าม comment และบรรทัดว่าง
            if(StringLen(line) == 0 || StringGetCharacter(line, 0) == '#')
                continue;
            
            // แยก key และ value
            int separatorPos = StringFind(line, "=");
            if(separatorPos > 0)
            {
                string key = StringSubstr(line, 0, separatorPos);
                string value = StringSubstr(line, separatorPos + 1);
                
                // แปลงค่าเป็นตัวเลข
                if(key == "RISK_PERCENT")
                    configRiskPercent = StringToDouble(value);
                else if(key == "STOP_LOSS_PIPS")
                    configStopLossPips = StringToDouble(value);
                else if(key == "TAKE_PROFIT_PIPS")
                    configTakeProfitPips = StringToDouble(value);
                else if(key == "RSI_PERIOD")
                    configRSIPeriod = (int)StringToInteger(value);
                else if(key == "MACD_FAST_EMA")
                    configMACDFastEMA = (int)StringToInteger(value);
                else if(key == "MACD_SLOW_EMA")
                    configMACDSlowEMA = (int)StringToInteger(value);
                else if(key == "MACD_SIGNAL_EMA")
                    configMACDSignalEMA = (int)StringToInteger(value);
                else if(key == "BOLLINGER_PERIOD")
                    configBollingerPeriod = (int)StringToInteger(value);
                else if(key == "BOLLINGER_DEVIATION")
                    configBollingerDeviation = StringToDouble(value);
            }
        }
        
        FileClose(fileHandle);
        Print("อ่านไฟล์การตั้งค่าสำเร็จ");
        return true;
    }
    else
    {
        Print("คำเตือน: ไม่สามารถเปิดไฟล์การตั้งค่าได้ ใช้ค่าเริ่มต้น");
        return true; // ใช้ค่าเริ่มต้นแทน
    }
}
