//+------------------------------------------------------------------+
//|                                        VolumeProfileCalculator.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Volume Profile Calculator Class                                  |
//+------------------------------------------------------------------+
class CVolumeProfileCalculator
{
private:
   VolumeProfileBin  m_bins[];                 // Volume profile bins
   int               m_bin_count;              // Number of bins
   double            m_bin_size;               // Size of each bin in price points
   double            m_min_price;              // Minimum price in range
   double            m_max_price;              // Maximum price in range
   int               m_smooth_window;          // Smoothing window size
   
   // Node detection parameters
   double            m_hvn_percentile;         // HVN percentile threshold
   double            m_lvn_percentile;         // LVN percentile threshold
   
   // Helper methods
   bool              CalculatePercentiles(double &p_hvn, double &p_lvn);
   bool              FindLocalMaxima(int &indices[], int &count);
   bool              FindLocalMinima(int &indices[], int &count);
   void              SmoothVolume();
   bool              MergeNearbyNodes(int &indices[], int &count, double min_distance);

public:
   // Constructor/Destructor
                     CVolumeProfileCalculator();
                    ~CVolumeProfileCalculator();
   
   // Configuration
   void              SetSmoothWindow(int window) { m_smooth_window = window; }
   void              SetHVNPercentile(double percentile) { m_hvn_percentile = percentile; }
   void              SetLVNPercentile(double percentile) { m_lvn_percentile = percentile; }
   
   // Main calculation methods
   bool              CalculateProfile(const TickData &ticks[], int tick_count,
                                    double bin_size_points, string symbol);
   bool              FindPOC(int &poc_index, double &poc_volume);
   bool              FindHVNNodes(int &indices[], int &count);
   bool              FindLVNNodes(int &indices[], int &count);
   
   // Data access
   int               GetBinCount() const { return m_bin_count; }
   bool              GetBin(int index, VolumeProfileBin &bin) const;
   double            GetBinPrice(int index) const;
   ulong             GetBinVolume(int index) const;
   ulong             GetBinVolumeSmooth(int index) const;
   
   // Utility methods
   int               PriceToBinIndex(double price) const;
   double            BinIndexToPrice(int index) const;
   void              Clear();
   
   // Statistics
   ulong             GetTotalVolume() const;
   double            GetVolumeWeightedPrice() const;
   void              PrintProfile(int max_lines = 20) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CVolumeProfileCalculator::CVolumeProfileCalculator()
{
   m_bin_count = 0;
   m_bin_size = 0;
   m_min_price = 0;
   m_max_price = 0;
   m_smooth_window = DEFAULT_SMOOTH_WINDOW;
   m_hvn_percentile = DEFAULT_HVN_PERCENTILE;
   m_lvn_percentile = DEFAULT_LVN_PERCENTILE;
   ArrayResize(m_bins, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CVolumeProfileCalculator::~CVolumeProfileCalculator()
{
   ArrayFree(m_bins);
}

//+------------------------------------------------------------------+
//| Calculate volume profile from tick data                          |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::CalculateProfile(const TickData &ticks[], int tick_count, 
                                               double bin_size_points, string symbol)
{
   if(tick_count <= 0)
   {
      Print("Error: No tick data provided");
      return false;
   }
   
   // Get symbol point value
   double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
   m_bin_size = bin_size_points * point;
   
   // Find price range
   m_min_price = ticks[0].price;
   m_max_price = ticks[0].price;
   
   for(int i = 1; i < tick_count; i++)
   {
      if(ticks[i].price < m_min_price)
         m_min_price = ticks[i].price;
      if(ticks[i].price > m_max_price)
         m_max_price = ticks[i].price;
   }
   
   // Calculate number of bins
   m_bin_count = (int)MathFloor((m_max_price - m_min_price) / m_bin_size) + 1;
   
   if(m_bin_count <= 0)
   {
      Print("Error: Invalid bin count: ", m_bin_count);
      return false;
   }
   
   // Initialize bins
   ArrayResize(m_bins, m_bin_count);
   for(int i = 0; i < m_bin_count; i++)
   {
      m_bins[i].price_level = m_min_price + i * m_bin_size + m_bin_size / 2.0;
      m_bins[i].volume = 0;
      m_bins[i].volume_smooth = 0;
      m_bins[i].tick_count = 0;
   }
   
   // Count volume per bin
   for(int i = 0; i < tick_count; i++)
   {
      int bin_index = PriceToBinIndex(ticks[i].price);
      if(bin_index >= 0 && bin_index < m_bin_count)
      {
         m_bins[bin_index].volume += (ticks[i].volume > 0) ? ticks[i].volume : 1;
         m_bins[bin_index].tick_count++;
      }
   }
   
   // Apply smoothing
   SmoothVolume();
   
   Print("Volume profile calculated: ", m_bin_count, " bins, ", 
         tick_count, " ticks processed");
   
   return true;
}

//+------------------------------------------------------------------+
//| Apply smoothing to volume data                                   |
//+------------------------------------------------------------------+
void CVolumeProfileCalculator::SmoothVolume()
{
   if(m_smooth_window <= 1)
   {
      // No smoothing, copy original values
      for(int i = 0; i < m_bin_count; i++)
         m_bins[i].volume_smooth = m_bins[i].volume;
      return;
   }
   
   int half_window = m_smooth_window / 2;
   
   for(int i = 0; i < m_bin_count; i++)
   {
      ulong sum = 0;
      int count = 0;
      
      for(int j = MathMax(0, i - half_window);
          j <= MathMin(m_bin_count - 1, i + half_window); j++)
      {
         sum += m_bins[j].volume;
         count++;
      }

      m_bins[i].volume_smooth = (count > 0) ? (ulong)(sum / count) : m_bins[i].volume;
   }
}

//+------------------------------------------------------------------+
//| Find Point of Control (highest volume bin)                       |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::FindPOC(int &poc_index, double &poc_volume)
{
   if(m_bin_count <= 0)
      return false;

   poc_index = 0;
   poc_volume = (double)m_bins[0].volume_smooth;

   for(int i = 1; i < m_bin_count; i++)
   {
      if((double)m_bins[i].volume_smooth > poc_volume)
      {
         poc_index = i;
         poc_volume = (double)m_bins[i].volume_smooth;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Calculate percentiles for HVN/LVN detection                      |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::CalculatePercentiles(double &p_hvn, double &p_lvn)
{
   if(m_bin_count <= 0)
      return false;
   
   // Create sorted array of volumes
   double volumes[];
   ArrayResize(volumes, m_bin_count);
   
   for(int i = 0; i < m_bin_count; i++)
      volumes[i] = (double)m_bins[i].volume_smooth;
   
   ArraySort(volumes);
   
   // Calculate percentile indices
   int hvn_index = (int)((m_hvn_percentile / 100.0) * (m_bin_count - 1));
   int lvn_index = (int)((m_lvn_percentile / 100.0) * (m_bin_count - 1));
   
   p_hvn = volumes[hvn_index];
   p_lvn = volumes[lvn_index];
   
   return true;
}

//+------------------------------------------------------------------+
//| Find High Volume Nodes                                           |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::FindHVNNodes(int &indices[], int &count)
{
   count = 0;
   if(m_bin_count <= 2)
      return false;
   
   double p_hvn, p_lvn;
   if(!CalculatePercentiles(p_hvn, p_lvn))
      return false;
   
   // Find local maxima above HVN threshold
   int maxima_indices[1000];
   int maxima_count;
   
   if(!FindLocalMaxima(maxima_indices, maxima_count))
      return false;
   
   // Filter by HVN threshold
   for(int i = 0; i < maxima_count && count < ArraySize(indices); i++)
   {
      int idx = maxima_indices[i];
      if(m_bins[idx].volume_smooth >= p_hvn)
      {
         indices[count] = idx;
         count++;
      }
   }
   
   // Merge nearby nodes
   int atr_handle = iATR(ChartSymbol(), PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double min_distance = 0.001; // Default value
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      min_distance = atr_values[0] * 0.25;
   }
   IndicatorRelease(atr_handle);
   MergeNearbyNodes(indices, count, min_distance);
   
   return true;
}

//+------------------------------------------------------------------+
//| Find Low Volume Nodes                                            |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::FindLVNNodes(int &indices[], int &count)
{
   count = 0;
   if(m_bin_count <= 2)
      return false;
   
   double p_hvn, p_lvn;
   if(!CalculatePercentiles(p_hvn, p_lvn))
      return false;
   
   // Find local minima below LVN threshold
   int minima_indices[1000];
   int minima_count;
   
   if(!FindLocalMinima(minima_indices, minima_count))
      return false;
   
   // Filter by LVN threshold and check if between HVN nodes
   for(int i = 0; i < minima_count && count < ArraySize(indices); i++)
   {
      int idx = minima_indices[i];
      if(m_bins[idx].volume_smooth <= p_lvn)
      {
         indices[count] = idx;
         count++;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Find local maxima                                                |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::FindLocalMaxima(int &indices[], int &count)
{
   count = 0;
   
   for(int i = 1; i < m_bin_count - 1; i++)
   {
      if(m_bins[i].volume_smooth > m_bins[i-1].volume_smooth &&
         m_bins[i].volume_smooth > m_bins[i+1].volume_smooth)
      {
         if(count < ArraySize(indices))
         {
            indices[count] = i;
            count++;
         }
      }
   }
   
   return count > 0;
}

//+------------------------------------------------------------------+
//| Find local minima                                                |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::FindLocalMinima(int &indices[], int &count)
{
   count = 0;
   
   for(int i = 1; i < m_bin_count - 1; i++)
   {
      if(m_bins[i].volume_smooth < m_bins[i-1].volume_smooth &&
         m_bins[i].volume_smooth < m_bins[i+1].volume_smooth)
      {
         if(count < ArraySize(indices))
         {
            indices[count] = i;
            count++;
         }
      }
   }
   
   return count > 0;
}

//+------------------------------------------------------------------+
//| Convert price to bin index                                       |
//+------------------------------------------------------------------+
int CVolumeProfileCalculator::PriceToBinIndex(double price) const
{
   if(m_bin_size <= 0)
      return -1;
   
   int index = (int)MathFloor((price - m_min_price) / m_bin_size);
   return MathMax(0, MathMin(index, m_bin_count - 1));
}

//+------------------------------------------------------------------+
//| Convert bin index to price                                       |
//+------------------------------------------------------------------+
double CVolumeProfileCalculator::BinIndexToPrice(int index) const
{
   if(index < 0 || index >= m_bin_count)
      return 0;
   
   return m_bins[index].price_level;
}

//+------------------------------------------------------------------+
//| Get bin data                                                     |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::GetBin(int index, VolumeProfileBin &bin) const
{
   if(index < 0 || index >= m_bin_count)
      return false;
   
   bin = m_bins[index];
   return true;
}

//+------------------------------------------------------------------+
//| Merge nearby nodes to avoid clustering                          |
//+------------------------------------------------------------------+
bool CVolumeProfileCalculator::MergeNearbyNodes(int &indices[], int &count, double min_distance)
{
   if(count <= 1)
      return false;

   bool merged = false;

   for(int i = 0; i < count - 1; i++)
   {
      for(int j = i + 1; j < count; j++)
      {
         double price1 = BinIndexToPrice(indices[i]);
         double price2 = BinIndexToPrice(indices[j]);
         double distance = MathAbs(price1 - price2);

         if(distance < min_distance)
         {
            // Remove the node with lower volume
            int remove_idx = (m_bins[indices[j]].volume_smooth > m_bins[indices[i]].volume_smooth) ? i : j;

            // Shift array elements
            for(int k = remove_idx; k < count - 1; k++)
               indices[k] = indices[k + 1];

            count--;
            merged = true;
            break;
         }
      }

      if(merged)
         break;
   }

   return merged;
}

//+------------------------------------------------------------------+
//| Clear all data                                                   |
//+------------------------------------------------------------------+
void CVolumeProfileCalculator::Clear()
{
   ArrayResize(m_bins, 0);
   m_bin_count = 0;
   m_bin_size = 0;
   m_min_price = 0;
   m_max_price = 0;
}
