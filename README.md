# TradingBots - โปรเจค EA สำหรับ MetaTrader 5

## รายละเอียดโปรเจค

โปรเจค TradingBots เป็นแพลตฟอร์มสำหรับการพัฒนาและจัดการ Expert Advisors (EA) หลายตัวสำหรับ MetaTrader 5 โดยออกแบบมาเพื่อรองรับการทำงานเป็นทีมและการสร้างบอทเทรดหลายตัวในโปรเจคเดียว

## โครงสร้างโปรเจค

```
TradingBots/
├── src/                    # โค้ดหลักของ EA ทั้งหมด
│   ├── bots/              # โฟลเดอร์สำหรับบอทแต่ละตัว
│   │   ├── GoldBot/       # บอทเทรดทองคำ
│   │   ├── ForexBot/      # บอทเทรดฟอเร็กซ์
│   │   └── CryptoBot/     # บอทเทรดคริปโต
│   ├── core/              # โค้ดหลักที่ใช้ร่วมกัน
│   │   ├── includes/      # ไฟล์ header และ library
│   │   ├── classes/       # คลาสต่างๆ
│   │   └── utils/         # ฟังก์ชันช่วยเหลือ
│   ├── strategies/        # กลยุทธ์การเทรด
│   └── templates/         # เทมเพลตสำหรับสร้างบอทใหม่
├── config/                # ไฟล์ config และการตั้งค่า
│   ├── global.conf        # การตั้งค่าทั่วไป
│   └── bots/              # การตั้งค่าเฉพาะบอทแต่ละตัว
├── docs/                  # เอกสารและคู่มือ
├── backtests/             # ผลการ backtest
├── tests/                 # ไฟล์สำหรับการทดสอบ
├── scripts/               # สคริปต์ช่วยเหลือ
└── README.md              # คู่มือการใช้งาน
```

## คุณสมบัติหลัก

- **Multi-Bot Support**: รองรับการสร้างและจัดการบอทหลายตัว
- **Team Collaboration**: โครงสร้างที่เหมาะสมสำหรับการทำงานเป็นทีม
- **Modular Design**: ออกแบบแบบโมดูลาร์เพื่อการบำรุงรักษาง่าย
- **Shared Components**: ใช้โค้ดร่วมกันระหว่างบอทต่างๆ
- **Configuration Management**: จัดการการตั้งค่าแบบแยกส่วน
- **Testing Framework**: ระบบทดสอบที่ครอบคลุม

## การติดตั้ง

1. คัดลอกโปรเจคไปยังโฟลเดอร์ `MQL5/Projects/`
2. เปิด MetaEditor 5
3. เปิดโปรเจค TradingBots
4. คอมไพล์บอทที่ต้องการใช้งาน
5. ลาก EA ไปยังชาร์ตที่ต้องการ

## การสร้างบอทใหม่

1. ใช้เทมเพลตจาก `src/templates/`
2. สร้างโฟลเดอร์ใหม่ใน `src/bots/`
3. ตั้งค่าการ config ใน `config/bots/`
4. เพิ่มเอกสารใน `docs/bots/`

## การตั้งค่า

- **Global Settings**: แก้ไขใน `config/global.conf`
- **Bot Settings**: แก้ไขใน `config/bots/[bot_name].conf`
- **Risk Management**: ตั้งค่าในแต่ละบอท

## การใช้งาน

1. เลือกบอทที่ต้องการใช้งาน
2. ตั้งค่าพารามิเตอร์ตามต้องการ
3. คอมไพล์และทดสอบ
4. ใช้งานจริงใน MT5

## การทำงานเป็นทีม

- ใช้ Git สำหรับการจัดการโค้ด
- แยกการทำงานตามบอทหรือโมดูล
- ใช้ Pull Request สำหรับการตรวจสอบโค้ด
- เอกสารการเปลี่ยนแปลงในทุกครั้ง

## หมายเหตุ

- ทดสอบบอทใน Account Demo ก่อน
- ใช้ Backtest เพื่อทดสอบกลยุทธ์
- ตรวจสอบการตั้งค่า Risk Management อย่างระมัดระวัง
- อัปเดตเอกสารเมื่อมีการเปลี่ยนแปลง
