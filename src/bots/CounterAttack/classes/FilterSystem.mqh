//+------------------------------------------------------------------+
//|                                                 FilterSystem.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Filter System Class                                              |
//+------------------------------------------------------------------+
class CFilterSystem
{
private:
   string            m_symbol;                 // Symbol name
   FilterParams      m_params;                 // Filter parameters
   
   // ATR values for calculations
   double            m_atr_current;            // Current ATR
   double            m_atr_median;             // Median ATR for comparison
   
   // Helper methods
   bool              CalculateATRValues();
   double            CalculateATRMedian(int lookback_periods = 50);
   bool              CheckRejectionCandle(int bar_index, double &wick_ratio);
   bool              CheckVolumeSpike(int bar_index, double &volume_ratio);
   double            GetTrendDirection();

public:
   // Constructor/Destructor
                     CFilterSystem(string symbol = "");
                    ~CFilterSystem();
   
   // Configuration
   void              SetFilterParams(const FilterParams &params) { m_params = params; }
   void              GetFilterParams(FilterParams &params) const { params = m_params; }
   
   // Individual filter methods (legacy - redirected to advanced versions)

   // Advanced filter methods
   bool              PassAdvancedVolatilityFilter(const ZoneInfo &zone);
   bool              PassAdvancedTrendFilter(const ZoneInfo &zone);
   bool              PassAdvancedPriceActionFilter(const ZoneInfo &zone);

   // Price action pattern detection
   bool              CheckPinBarRejection(int bar_index, const ZoneInfo &zone, string &rejection_type);
   bool              CheckEngulfingRejection(int bar_index, const ZoneInfo &zone, string &rejection_type);
   bool              CheckLongWickRejection(int bar_index, const ZoneInfo &zone, string &rejection_type);
   bool              CheckVolumeConfirmedRejection(int bar_index, const ZoneInfo &zone, string &rejection_type);

   // Enhanced trend analysis
   double            GetAdvancedTrendDirection();
   double            GetTrendStrength();

   // Volume analysis
   bool              CheckVolumeSpike(int bar_index);
   
   // Combined filter method
   bool              PassAllFilters(const ZoneInfo &zone);
   
   // Filter zone arrays
   bool              FilterZones(const ZoneInfo &input_zones[], int input_count,
                               ZoneInfo &output_zones[], int &output_count);
   
   // Utility methods
   void              UpdateATRValues();
   void              PrintFilterStatus() const;
   
   // Default filter parameters
   static FilterParams GetDefaultParams();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFilterSystem::CFilterSystem(string symbol = "")
{
   m_symbol = (symbol == "") ? ChartSymbol() : symbol;
   m_params = GetDefaultParams();
   m_atr_current = 0;
   m_atr_median = 0;
   
   UpdateATRValues();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFilterSystem::~CFilterSystem()
{
   // Nothing to clean up
}

//+------------------------------------------------------------------+
//| Get default filter parameters                                    |
//+------------------------------------------------------------------+
static FilterParams CFilterSystem::GetDefaultParams()
{
   FilterParams params;
   params.use_volatility_filter = true;
   params.use_trend_filter = true;
   params.use_price_action_filter = true;

   // Advanced Volatility Filter
   params.min_atr_ratio = 1.0;           // ATR should be >= median
   params.atr_lookback_periods = 50;     // 50 periods for median calculation

   // Advanced Trend Filter
   params.trend_ma_period = 50;          // Slow EMA 50 for trend
   params.trend_ma_fast_period = 25;     // Fast EMA 25 for trend detection
   params.trend_strength_threshold = 0.1; // 0.1% minimum trend strength

   // Price Action Filter
   params.rejection_wick_ratio = 0.6;    // 60% wick for rejection
   params.volume_spike_ratio = 1.2;      // 120% of average volume
   params.volume_lookback_periods = 20;  // 20 periods for volume average

   return params;
}

//+------------------------------------------------------------------+
//| Update ATR values                                                |
//+------------------------------------------------------------------+
void CFilterSystem::UpdateATRValues()
{
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      m_atr_current = atr_values[0];
   }
   else
   {
      m_atr_current = 0.001; // Default value
   }
   IndicatorRelease(atr_handle);
   m_atr_median = CalculateATRMedian();
}

//+------------------------------------------------------------------+
//| Calculate ATR median for comparison                              |
//+------------------------------------------------------------------+
double CFilterSystem::CalculateATRMedian(int lookback_periods = 50)
{
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, lookback_periods);

   if(CopyBuffer(atr_handle, 0, 1, lookback_periods, atr_values) <= 0)
   {
      IndicatorRelease(atr_handle);
      return 0.001; // Default value
   }

   IndicatorRelease(atr_handle);
   ArraySort(atr_values);

   int median_index = ArraySize(atr_values) / 2;
   return atr_values[median_index];
}

//+------------------------------------------------------------------+
//| Advanced Volatility Filter - Enhanced ATR Analysis              |
//+------------------------------------------------------------------+
bool CFilterSystem::PassAdvancedVolatilityFilter(const ZoneInfo &zone)
{
   if(!m_params.use_volatility_filter)
      return true;

   // Calculate ATR median using configured lookback periods
   double atr_median = CalculateATRMedian(m_params.atr_lookback_periods);
   double atr_ratio = (atr_median > 0) ? m_atr_current / atr_median : 1.0;

   // Check if current ATR meets minimum ratio requirement
   bool atr_check = atr_ratio >= m_params.min_atr_ratio;

   // Additional check: Market should not be in extremely low volatility
   // (ATR should not be in bottom 20% of recent values)
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, m_params.atr_lookback_periods);

   bool not_extremely_low_vol = true; // Default to true
   double atr_20th_percentile = 0.0; // Declare outside if scope
   if(CopyBuffer(atr_handle, 0, 1, m_params.atr_lookback_periods, atr_values) > 0)
   {
      ArraySort(atr_values);
      atr_20th_percentile = atr_values[m_params.atr_lookback_periods / 5]; // 20th percentile
      not_extremely_low_vol = m_atr_current > atr_20th_percentile;
   }
   IndicatorRelease(atr_handle);

   bool pass = atr_check && not_extremely_low_vol;

   if(!pass)
   {
      Print("Zone failed advanced volatility filter. ATR ratio: ", DoubleToString(atr_ratio, 3),
            " Required: ", DoubleToString(m_params.min_atr_ratio, 3),
            " Current ATR: ", DoubleToString(m_atr_current, 5),
            " 20th percentile: ", DoubleToString(atr_20th_percentile, 5));
   }

   return pass;
}



//+------------------------------------------------------------------+
//| Advanced Trend Filter - Enhanced trend analysis                 |
//+------------------------------------------------------------------+
bool CFilterSystem::PassAdvancedTrendFilter(const ZoneInfo &zone)
{
   if(!m_params.use_trend_filter)
      return true;

   // Get multiple timeframe trend analysis
   double trend_direction = GetAdvancedTrendDirection();
   double trend_strength = GetTrendStrength();
   double current_price = iClose(m_symbol, PERIOD_H1, 0);

   bool pass = true;
   string reason = "";

   // Enhanced logic based on zone type and trend context
   if(zone.type == ZONE_HVN && zone.mid_price > current_price)
   {
      // Resistance zone - prefer downtrend or weak trend
      if(trend_direction > m_params.trend_strength_threshold && trend_strength > 0.5)
      {
         pass = false;
         reason = "Strong uptrend against resistance zone";
      }
   }
   else if(zone.type == ZONE_HVN && zone.mid_price < current_price)
   {
      // Support zone - prefer uptrend or weak trend
      if(trend_direction < -m_params.trend_strength_threshold && trend_strength > 0.5)
      {
         pass = false;
         reason = "Strong downtrend against support zone";
      }
   }
   else if(zone.type == ZONE_LVN)
   {
      // Breakout zone - need sufficient trend strength
      if(trend_strength < 0.3)
      {
         pass = false;
         reason = "Insufficient trend strength for LVN breakout";
      }
   }
   else if(zone.type == ZONE_POC)
   {
      // POC zones work in any trend but avoid extreme trends
      if(trend_strength > 0.8)
      {
         pass = false;
         reason = "Extremely strong trend may override POC";
      }
   }

   if(!pass)
   {
      Print("Zone failed advanced trend filter. Reason: ", reason,
            " Trend direction: ", DoubleToString(trend_direction, 3),
            " Trend strength: ", DoubleToString(trend_strength, 3),
            " Zone type: ", EnumToString(zone.type));
   }

   return pass;
}

//+------------------------------------------------------------------+
//| Get advanced trend direction with multiple MA analysis          |
//+------------------------------------------------------------------+
double CFilterSystem::GetAdvancedTrendDirection()
{
   int ma_fast_handle = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_fast_period, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow_handle = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_period, 0, MODE_EMA, PRICE_CLOSE);

   double fast_values[], slow_values[];
   ArrayResize(fast_values, 1);
   ArrayResize(slow_values, 1);
   double ema_fast = 0, ema_slow = 0;

   if(CopyBuffer(ma_fast_handle, 0, 0, 1, fast_values) > 0)
      ema_fast = fast_values[0];
   if(CopyBuffer(ma_slow_handle, 0, 0, 1, slow_values) > 0)
      ema_slow = slow_values[0];

   IndicatorRelease(ma_fast_handle);
   IndicatorRelease(ma_slow_handle);

   if(ema_slow == 0)
      return 0;

   // Calculate normalized trend direction
   double trend_ratio = (ema_fast - ema_slow) / ema_slow;

   // Additional confirmation from price position relative to MAs
   double current_price = iClose(m_symbol, PERIOD_H1, 0);
   double price_vs_fast = (current_price - ema_fast) / ema_fast;
   double price_vs_slow = (current_price - ema_slow) / ema_slow;

   // Weighted average for more robust signal
   double combined_signal = (trend_ratio * 0.5) + (price_vs_fast * 0.3) + (price_vs_slow * 0.2);

   // Normalize to -1 to +1 range
   return MathMax(-1.0, MathMin(1.0, combined_signal * 100));
}

//+------------------------------------------------------------------+
//| Calculate trend strength (0 = no trend, 1 = very strong trend)  |
//+------------------------------------------------------------------+
double CFilterSystem::GetTrendStrength()
{
   // Use multiple indicators for trend strength
   int ma_fast_handle = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_fast_period, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow_handle = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_period, 0, MODE_EMA, PRICE_CLOSE);

   double fast_values[], slow_values[];
   ArrayResize(fast_values, 1);
   ArrayResize(slow_values, 1);
   double ema_fast = 0, ema_slow = 0;

   if(CopyBuffer(ma_fast_handle, 0, 0, 1, fast_values) > 0)
      ema_fast = fast_values[0];
   if(CopyBuffer(ma_slow_handle, 0, 0, 1, slow_values) > 0)
      ema_slow = slow_values[0];

   IndicatorRelease(ma_fast_handle);
   IndicatorRelease(ma_slow_handle);

   // 1. MA separation as trend strength indicator
   double ma_separation = MathAbs(ema_fast - ema_slow) / ema_slow;

   // 2. Price momentum (how far price is from slow MA)
   double current_price = iClose(m_symbol, PERIOD_H1, 0);
   double price_momentum = MathAbs(current_price - ema_slow) / ema_slow;

   // 3. Directional consistency over recent bars
   double directional_consistency = 0;
   int consistent_bars = 0;
   bool initial_direction = (iClose(m_symbol, PERIOD_H1, 1) > iClose(m_symbol, PERIOD_H1, 2));

   for(int i = 1; i < 10; i++) // Check last 10 bars
   {
      bool current_direction = (iClose(m_symbol, PERIOD_H1, i) > iClose(m_symbol, PERIOD_H1, i + 1));
      if(current_direction == initial_direction)
         consistent_bars++;
   }
   directional_consistency = consistent_bars / 10.0;

   // Combine all factors
   double strength = (ma_separation * 0.4) + (price_momentum * 0.3) + (directional_consistency * 0.3);

   // Normalize to 0-1 range
   return MathMin(1.0, strength * 50); // Scale factor may need adjustment
}

//+------------------------------------------------------------------+
//| Get trend direction (-1 to +1, negative = downtrend)            |
//+------------------------------------------------------------------+
double CFilterSystem::GetTrendDirection()
{
   double ema_fast = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_period / 2, 0, MODE_EMA, PRICE_CLOSE);
   double ema_slow = iMA(m_symbol, PERIOD_H1, m_params.trend_ma_period, 0, MODE_EMA, PRICE_CLOSE);
   
   if(ema_slow == 0)
      return 0;
   
   // Normalize the difference
   double diff_ratio = (ema_fast - ema_slow) / ema_slow;
   
   // Clamp to -1 to +1 range
   return MathMax(-1.0, MathMin(1.0, diff_ratio * 100));
}

//+------------------------------------------------------------------+
//| Advanced Price Action Filter - Enhanced rejection analysis      |
//+------------------------------------------------------------------+
bool CFilterSystem::PassAdvancedPriceActionFilter(const ZoneInfo &zone)
{
   if(!m_params.use_price_action_filter)
      return true;

   // Look for multiple types of rejection signals
   int bars_to_check = 5; // Check last 5 bars
   bool found_rejection = false;
   string rejection_type = "";

   for(int i = 1; i <= bars_to_check; i++)
   {
      double open = iOpen(m_symbol, PERIOD_H1, i);
      double high = iHigh(m_symbol, PERIOD_H1, i);
      double low = iLow(m_symbol, PERIOD_H1, i);
      double close = iClose(m_symbol, PERIOD_H1, i);

      // Check if bar touched the zone
      bool touched_zone = (high >= zone.lower_price && low <= zone.upper_price);

      if(!touched_zone)
         continue;

      // Check for different types of rejection patterns
      if(CheckPinBarRejection(i, zone, rejection_type))
      {
         found_rejection = true;
         break;
      }

      if(CheckEngulfingRejection(i, zone, rejection_type))
      {
         found_rejection = true;
         break;
      }

      if(CheckLongWickRejection(i, zone, rejection_type))
      {
         found_rejection = true;
         break;
      }

      if(CheckVolumeConfirmedRejection(i, zone, rejection_type))
      {
         found_rejection = true;
         break;
      }
   }

   if(!found_rejection && m_params.use_price_action_filter)
   {
      Print("Zone failed advanced price action filter - no rejection signal found");
   }
   else if(found_rejection)
   {
      Print("Zone passed price action filter - ", rejection_type, " detected");
   }

   return found_rejection || !m_params.use_price_action_filter;
}

//+------------------------------------------------------------------+
//| Check for Pin Bar rejection pattern                             |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckPinBarRejection(int bar_index, const ZoneInfo &zone, string &rejection_type)
{
   double open = iOpen(m_symbol, PERIOD_H1, bar_index);
   double high = iHigh(m_symbol, PERIOD_H1, bar_index);
   double low = iLow(m_symbol, PERIOD_H1, bar_index);
   double close = iClose(m_symbol, PERIOD_H1, bar_index);

   double body_size = MathAbs(close - open);
   double total_range = high - low;
   double upper_wick = high - MathMax(open, close);
   double lower_wick = MathMin(open, close) - low;

   if(total_range == 0)
      return false;

   // Pin bar criteria: small body, long wick in rejection direction
   double body_ratio = body_size / total_range;
   bool small_body = body_ratio < 0.3; // Body less than 30% of total range

   // Check for rejection from resistance (upper wick)
   if(zone.mid_price > close && upper_wick / total_range > 0.6)
   {
      if(small_body && CheckVolumeSpike(bar_index))
      {
         rejection_type = "Pin Bar Resistance Rejection";
         return true;
      }
   }

   // Check for rejection from support (lower wick)
   if(zone.mid_price < close && lower_wick / total_range > 0.6)
   {
      if(small_body && CheckVolumeSpike(bar_index))
      {
         rejection_type = "Pin Bar Support Rejection";
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check for Engulfing rejection pattern                           |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckEngulfingRejection(int bar_index, const ZoneInfo &zone, string &rejection_type)
{
   if(bar_index >= Bars(m_symbol, PERIOD_H1) - 1)
      return false;

   // Current bar
   double open1 = iOpen(m_symbol, PERIOD_H1, bar_index);
   double high1 = iHigh(m_symbol, PERIOD_H1, bar_index);
   double low1 = iLow(m_symbol, PERIOD_H1, bar_index);
   double close1 = iClose(m_symbol, PERIOD_H1, bar_index);

   // Previous bar
   double open2 = iOpen(m_symbol, PERIOD_H1, bar_index + 1);
   double high2 = iHigh(m_symbol, PERIOD_H1, bar_index + 1);
   double low2 = iLow(m_symbol, PERIOD_H1, bar_index + 1);
   double close2 = iClose(m_symbol, PERIOD_H1, bar_index + 1);

   bool current_bullish = close1 > open1;
   bool previous_bearish = close2 < open2;
   bool current_bearish = close1 < open1;
   bool previous_bullish = close2 > open2;

   // Bullish engulfing at support
   if(zone.mid_price < close1 && current_bullish && previous_bearish)
   {
      if(close1 > open2 && open1 < close2 && CheckVolumeSpike(bar_index))
      {
         rejection_type = "Bullish Engulfing Support Rejection";
         return true;
      }
   }

   // Bearish engulfing at resistance
   if(zone.mid_price > close1 && current_bearish && previous_bullish)
   {
      if(close1 < open2 && open1 > close2 && CheckVolumeSpike(bar_index))
      {
         rejection_type = "Bearish Engulfing Resistance Rejection";
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check for Long Wick rejection pattern                           |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckLongWickRejection(int bar_index, const ZoneInfo &zone, string &rejection_type)
{
   double open = iOpen(m_symbol, PERIOD_H1, bar_index);
   double high = iHigh(m_symbol, PERIOD_H1, bar_index);
   double low = iLow(m_symbol, PERIOD_H1, bar_index);
   double close = iClose(m_symbol, PERIOD_H1, bar_index);

   double total_range = high - low;
   double upper_wick = high - MathMax(open, close);
   double lower_wick = MathMin(open, close) - low;

   if(total_range == 0)
      return false;

   double wick_threshold = m_params.rejection_wick_ratio; // Default 0.6

   // Long upper wick rejection from resistance
   if(zone.mid_price > close && upper_wick / total_range > wick_threshold)
   {
      // Additional confirmation: close should be in lower half of range
      if((close - low) / total_range < 0.5)
      {
         rejection_type = "Long Upper Wick Resistance Rejection";
         return true;
      }
   }

   // Long lower wick rejection from support
   if(zone.mid_price < close && lower_wick / total_range > wick_threshold)
   {
      // Additional confirmation: close should be in upper half of range
      if((high - close) / total_range < 0.5)
      {
         rejection_type = "Long Lower Wick Support Rejection";
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check for Volume Confirmed rejection                            |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckVolumeConfirmedRejection(int bar_index, const ZoneInfo &zone, string &rejection_type)
{
   double volume_ratio;
   if(!CheckVolumeSpike(bar_index, volume_ratio))
      return false;

   // Must have significant volume spike (150%+ of average)
   if(volume_ratio < 1.5)
      return false;

   double open = iOpen(m_symbol, PERIOD_H1, bar_index);
   double high = iHigh(m_symbol, PERIOD_H1, bar_index);
   double low = iLow(m_symbol, PERIOD_H1, bar_index);
   double close = iClose(m_symbol, PERIOD_H1, bar_index);

   // Check if price action shows rejection with high volume
   bool price_rejected_from_zone = false;

   if(zone.mid_price > close) // Resistance zone
   {
      // Price should have moved away from zone with high volume
      if(high >= zone.lower_price && close < zone.mid_price)
      {
         price_rejected_from_zone = true;
         rejection_type = "High Volume Resistance Rejection";
      }
   }
   else // Support zone
   {
      // Price should have moved away from zone with high volume
      if(low <= zone.upper_price && close > zone.mid_price)
      {
         price_rejected_from_zone = true;
         rejection_type = "High Volume Support Rejection";
      }
   }

   return price_rejected_from_zone;
}

//+------------------------------------------------------------------+
//| Check for rejection candle pattern                              |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckRejectionCandle(int bar_index, double &wick_ratio)
{
   double open = iOpen(m_symbol, PERIOD_H1, bar_index);
   double high = iHigh(m_symbol, PERIOD_H1, bar_index);
   double low = iLow(m_symbol, PERIOD_H1, bar_index);
   double close = iClose(m_symbol, PERIOD_H1, bar_index);
   
   double body_size = MathAbs(close - open);
   double total_range = high - low;
   
   if(total_range == 0)
   {
      wick_ratio = 0;
      return false;
   }
   
   // Calculate upper and lower wick sizes
   double upper_wick = high - MathMax(open, close);
   double lower_wick = MathMin(open, close) - low;
   
   // Check for significant wick (rejection)
   double max_wick = MathMax(upper_wick, lower_wick);
   wick_ratio = max_wick / total_range;
   
   return wick_ratio >= m_params.rejection_wick_ratio;
}

//+------------------------------------------------------------------+
//| Check for volume spike with enhanced analysis                   |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckVolumeSpike(int bar_index, double &volume_ratio)
{
   // For tick volume (since real volume might not be available)
   long current_volume = iTickVolume(m_symbol, PERIOD_H1, bar_index);

   // Calculate average volume using configured lookback periods
   long total_volume = 0;
   int count = 0;
   int lookback = m_params.volume_lookback_periods;

   for(int i = bar_index + 1; i <= bar_index + lookback; i++)
   {
      long vol = iTickVolume(m_symbol, PERIOD_H1, i);
      if(vol > 0)
      {
         total_volume += vol;
         count++;
      }
   }

   if(count == 0)
   {
      volume_ratio = 1.0;
      return false;
   }

   double average_volume = (double)total_volume / count;
   volume_ratio = (average_volume > 0) ? current_volume / average_volume : 1.0;

   return volume_ratio >= m_params.volume_spike_ratio;
}

//+------------------------------------------------------------------+
//| Overloaded CheckVolumeSpike for simple boolean check            |
//+------------------------------------------------------------------+
bool CFilterSystem::CheckVolumeSpike(int bar_index)
{
   double volume_ratio;
   return CheckVolumeSpike(bar_index, volume_ratio);
}

//+------------------------------------------------------------------+
//| Check if zone passes all enabled filters (Enhanced Version)     |
//+------------------------------------------------------------------+
bool CFilterSystem::PassAllFilters(const ZoneInfo &zone)
{
   // Use advanced filters for better accuracy
   if(!PassAdvancedVolatilityFilter(zone))
      return false;

   if(!PassAdvancedTrendFilter(zone))
      return false;

   if(!PassAdvancedPriceActionFilter(zone))
      return false;

   return true;
}



//+------------------------------------------------------------------+
//| Filter array of zones                                           |
//+------------------------------------------------------------------+
bool CFilterSystem::FilterZones(const ZoneInfo &input_zones[], int input_count,
                               ZoneInfo &output_zones[], int &output_count)
{
   output_count = 0;
   
   if(input_count <= 0)
      return false;
   
   // Update ATR values before filtering
   UpdateATRValues();
   
   // Count zones that pass filters
   for(int i = 0; i < input_count; i++)
   {
      if(PassAllFilters(input_zones[i]))
         output_count++;
   }
   
   if(output_count == 0)
      return false;
   
   ArrayResize(output_zones, output_count);
   int index = 0;
   
   for(int i = 0; i < input_count; i++)
   {
      if(PassAllFilters(input_zones[i]))
      {
         output_zones[index] = input_zones[i];
         index++;
      }
   }
   
   Print("Filter results: ", output_count, " zones passed out of ", input_count);
   
   return true;
}

//+------------------------------------------------------------------+
//| Print filter status                                              |
//+------------------------------------------------------------------+
void CFilterSystem::PrintFilterStatus() const
{
   Print("=== Filter System Status ===");
   Print("Volatility Filter: ", m_params.use_volatility_filter ? "ON" : "OFF");
   Print("Trend Filter: ", m_params.use_trend_filter ? "ON" : "OFF");
   Print("Price Action Filter: ", m_params.use_price_action_filter ? "ON" : "OFF");
   Print("Current ATR: ", DoubleToString(m_atr_current, 5));
   Print("Median ATR: ", DoubleToString(m_atr_median, 5));
   Print("ATR Ratio: ", DoubleToString(m_atr_current / m_atr_median, 3));
}
