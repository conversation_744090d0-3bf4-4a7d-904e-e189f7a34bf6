//+------------------------------------------------------------------+
//|                                    SimpleEA_UsingCounterAttack.mq5 |
//|                                  Example EA using CounterAttack  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Example"
#property version   "1.00"
#property description "Simple EA demonstrating how to use CounterAttack zones"

//--- Input Parameters
input double InpLotSize = 0.01;                    // Lot size
input int    InpStopLossPips = 50;                 // Stop loss in pips
input int    InpTakeProfitPips = 100;              // Take profit in pips
input double InpMinZoneScore = 7.0;                // Minimum zone score to trade
input bool   InpTradeHVNOnly = true;               // Trade HVN zones only
input int    InpMagicNumber = 123456;              // Magic number

//--- Global Variables
string g_zonesFile = "";
datetime g_lastFileCheck = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Generate zones filename
   string symbol = ChartSymbol();
   string timeframe = EnumToString(ChartPeriod());
   g_zonesFile = "fresh_zones_" + symbol + "_" + timeframe + ".csv";
   
   Print("Simple EA initialized. Monitoring zones file: ", g_zonesFile);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new zones every 30 seconds
   datetime current_time = TimeCurrent();
   if(current_time - g_lastFileCheck < 30)
      return;
   
   g_lastFileCheck = current_time;
   
   // Read zones from file
   ZoneData zones[];
   int zone_count = ReadZonesFromFile(zones);
   
   if(zone_count <= 0)
      return;
   
   // Check for trading opportunities
   CheckTradingOpportunities(zones, zone_count);
}

//+------------------------------------------------------------------+
//| Zone data structure                                              |
//+------------------------------------------------------------------+
struct ZoneData
{
   string   type;           // Zone type (POC/HVN/LVN)
   double   mid_price;      // Mid price
   double   upper_price;    // Upper boundary
   double   lower_price;    // Lower boundary
   string   status;         // Zone status
   double   score;          // Zone score
};

//+------------------------------------------------------------------+
//| Read zones from CounterAttack output file                       |
//+------------------------------------------------------------------+
int ReadZonesFromFile(ZoneData &zones[])
{
   int file_handle = FileOpen(g_zonesFile, FILE_READ | FILE_CSV);
   if(file_handle == INVALID_HANDLE)
      return 0;
   
   // Skip header line
   if(FileIsEnding(file_handle))
   {
      FileClose(file_handle);
      return 0;
   }
   
   string header = FileReadString(file_handle);
   
   // Count lines first
   int line_count = 0;
   while(!FileIsEnding(file_handle))
   {
      FileReadString(file_handle);
      line_count++;
   }
   
   FileClose(file_handle);
   
   if(line_count <= 0)
      return 0;
   
   // Read actual data
   file_handle = FileOpen(g_zonesFile, FILE_READ | FILE_CSV);
   if(file_handle == INVALID_HANDLE)
      return 0;
   
   FileReadString(file_handle); // Skip header
   
   ArrayResize(zones, line_count);
   int index = 0;
   
   while(!FileIsEnding(file_handle) && index < line_count)
   {
      string timestamp = FileReadString(file_handle);
      zones[index].type = FileReadString(file_handle);
      zones[index].mid_price = StringToDouble(FileReadString(file_handle));
      zones[index].upper_price = StringToDouble(FileReadString(file_handle));
      zones[index].lower_price = StringToDouble(FileReadString(file_handle));
      zones[index].status = FileReadString(file_handle);
      zones[index].score = StringToDouble(FileReadString(file_handle));
      
      // Skip remaining columns
      FileReadString(file_handle); // volume
      FileReadString(file_handle); // created_at
      
      index++;
   }
   
   FileClose(file_handle);
   
   Print("Read ", index, " zones from file");
   return index;
}

//+------------------------------------------------------------------+
//| Check for trading opportunities                                  |
//+------------------------------------------------------------------+
void CheckTradingOpportunities(const ZoneData &zones[], int zone_count)
{
   double current_price = SymbolInfoDouble(ChartSymbol(), SYMBOL_BID);
   
   for(int i = 0; i < zone_count; i++)
   {
      // Filter zones
      if(zones[i].score < InpMinZoneScore)
         continue;
      
      if(zones[i].status != "ZONE_FRESH")
         continue;
      
      if(InpTradeHVNOnly && zones[i].type != "ZONE_HVN")
         continue;
      
      // Check if price is near zone
      bool near_zone = (current_price >= zones[i].lower_price && 
                       current_price <= zones[i].upper_price);
      
      if(!near_zone)
         continue;
      
      // Determine trade direction
      bool is_resistance = (zones[i].mid_price > current_price);
      bool is_support = (zones[i].mid_price < current_price);
      
      // Check for existing positions
      if(HasOpenPosition())
         continue;
      
      // Look for entry signals
      if(is_resistance && CheckSellSignal(zones[i]))
      {
         OpenSellOrder(zones[i]);
      }
      else if(is_support && CheckBuySignal(zones[i]))
      {
         OpenBuyOrder(zones[i]);
      }
   }
}

//+------------------------------------------------------------------+
//| Check for sell signal at resistance zone                        |
//+------------------------------------------------------------------+
bool CheckSellSignal(const ZoneData &zone)
{
   // Simple signal: bearish candle at resistance
   double open = iOpen(ChartSymbol(), PERIOD_CURRENT, 1);
   double close = iClose(ChartSymbol(), PERIOD_CURRENT, 1);
   double high = iHigh(ChartSymbol(), PERIOD_CURRENT, 1);
   
   // Bearish candle that touched the zone
   bool bearish_candle = (close < open);
   bool touched_zone = (high >= zone.lower_price);
   
   return bearish_candle && touched_zone;
}

//+------------------------------------------------------------------+
//| Check for buy signal at support zone                            |
//+------------------------------------------------------------------+
bool CheckBuySignal(const ZoneData &zone)
{
   // Simple signal: bullish candle at support
   double open = iOpen(ChartSymbol(), PERIOD_CURRENT, 1);
   double close = iClose(ChartSymbol(), PERIOD_CURRENT, 1);
   double low = iLow(ChartSymbol(), PERIOD_CURRENT, 1);
   
   // Bullish candle that touched the zone
   bool bullish_candle = (close > open);
   bool touched_zone = (low <= zone.upper_price);
   
   return bullish_candle && touched_zone;
}

//+------------------------------------------------------------------+
//| Check if there's an open position                               |
//+------------------------------------------------------------------+
bool HasOpenPosition()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) == ChartSymbol() &&
            PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
         {
            return true;
         }
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Open sell order                                                  |
//+------------------------------------------------------------------+
void OpenSellOrder(const ZoneData &zone)
{
   double price = SymbolInfoDouble(ChartSymbol(), SYMBOL_BID);
   double point = SymbolInfoDouble(ChartSymbol(), SYMBOL_POINT);
   
   double sl = price + InpStopLossPips * point * 10;
   double tp = price - InpTakeProfitPips * point * 10;
   
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = ChartSymbol();
   request.volume = InpLotSize;
   request.type = ORDER_TYPE_SELL;
   request.price = price;
   request.sl = sl;
   request.tp = tp;
   request.magic = InpMagicNumber;
   request.comment = "CounterAttack Sell @ " + DoubleToString(zone.mid_price, 5);
   
   if(OrderSend(request, result))
   {
      Print("Sell order opened at zone: ", zone.mid_price, " Score: ", zone.score);
   }
   else
   {
      Print("Failed to open sell order. Error: ", result.retcode);
   }
}

//+------------------------------------------------------------------+
//| Open buy order                                                   |
//+------------------------------------------------------------------+
void OpenBuyOrder(const ZoneData &zone)
{
   double price = SymbolInfoDouble(ChartSymbol(), SYMBOL_ASK);
   double point = SymbolInfoDouble(ChartSymbol(), SYMBOL_POINT);
   
   double sl = price - InpStopLossPips * point * 10;
   double tp = price + InpTakeProfitPips * point * 10;
   
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = ChartSymbol();
   request.volume = InpLotSize;
   request.type = ORDER_TYPE_BUY;
   request.price = price;
   request.sl = sl;
   request.tp = tp;
   request.magic = InpMagicNumber;
   request.comment = "CounterAttack Buy @ " + DoubleToString(zone.mid_price, 5);
   
   if(OrderSend(request, result))
   {
      Print("Buy order opened at zone: ", zone.mid_price, " Score: ", zone.score);
   }
   else
   {
      Print("Failed to open buy order. Error: ", result.retcode);
   }
}
