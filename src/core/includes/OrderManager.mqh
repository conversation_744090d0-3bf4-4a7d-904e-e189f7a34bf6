//+------------------------------------------------------------------+
//| OrderManager.mqh - การจัดการ Order และการเทรด
//+------------------------------------------------------------------+
#property copyright "MyFirstBot"
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>
#include "Constants.mqh"

//--- คลาสสำหรับการจัดการ Order
class COrderManager
{
private:
    CTrade trade;                    // วัตถุสำหรับการเทรด
    string symbol;                   // สัญลักษณ์ที่เทรด
    double lotSize;                  // ขนาด Lot
    double stopLoss;                 // Stop Loss
    double takeProfit;               // Take Profit
    
public:
    //--- Constructor
    COrderManager();
    
    //--- Destructor
    ~COrderManager();
    
    //--- ฟังก์ชันการตั้งค่า
    void SetSymbol(string _symbol);
    void SetLotSize(double _lotSize);
    void SetStopLoss(double _stopLoss);
    void SetTakeProfit(double _takeProfit);
    
    //--- ฟังก์ชันการเทรด
    bool OpenBuyOrder();
    bool OpenSellOrder();
    bool CloseOrder(ulong ticket);
    bool CloseAllOrders();
    
    //--- ฟังก์ชันการตรวจสอบ
    int GetTotalOrders();
    bool HasOpenOrders();
    double GetCurrentProfit();
    
    //--- ฟังก์ชันการคำนวณ
    double CalculateLotSize(double riskPercent, double stopLossPips);
    double PipsToPrice(double pips);
    double PriceToPips(double price);
    
    //--- ฟังก์ชันการติดตาม Order
    ulong GetLastOrderTicket();
};

//--- Constructor
COrderManager::COrderManager()
{
    symbol = Symbol();
    lotSize = MIN_LOT;
    stopLoss = 0.0;
    takeProfit = 0.0;
    
    // ตั้งค่า Trade object
    trade.SetExpertMagicNumber(123456);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
}

//--- Destructor
COrderManager::~COrderManager()
{
    // ปิด Order ทั้งหมดเมื่อปิด EA
    CloseAllOrders();
}

//--- ตั้งค่าสัญลักษณ์
void COrderManager::SetSymbol(string _symbol)
{
    symbol = _symbol;
}

//--- ตั้งค่าขนาด Lot
void COrderManager::SetLotSize(double _lotSize)
{
    if(_lotSize >= MIN_LOT && _lotSize <= MAX_LOT)
        lotSize = _lotSize;
}

//--- ตั้งค่า Stop Loss
void COrderManager::SetStopLoss(double _stopLoss)
{
    stopLoss = _stopLoss;
}

//--- ตั้งค่า Take Profit
void COrderManager::SetTakeProfit(double _takeProfit)
{
    takeProfit = _takeProfit;
}

//--- เปิด Order Buy
bool COrderManager::OpenBuyOrder()
{
    if(HasOpenOrders())
        return false;
        
    double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
    double sl = (stopLoss > 0) ? ask - stopLoss : 0;
    double tp = (takeProfit > 0) ? ask + takeProfit : 0;
    
    return trade.Buy(lotSize, symbol, ask, sl, tp, "MyFirstBot Buy");
}

//--- เปิด Order Sell
bool COrderManager::OpenSellOrder()
{
    if(HasOpenOrders())
        return false;
        
    double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
    double sl = (stopLoss > 0) ? bid + stopLoss : 0;
    double tp = (takeProfit > 0) ? bid - takeProfit : 0;
    
    return trade.Sell(lotSize, symbol, bid, sl, tp, "MyFirstBot Sell");
}

//--- ปิด Order ตาม Ticket
bool COrderManager::CloseOrder(ulong ticket)
{
    return trade.PositionClose(ticket);
}

//--- ปิด Order ทั้งหมด
bool COrderManager::CloseAllOrders()
{
    bool result = true;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(!trade.PositionClose(ticket))
                result = false;
        }
    }
    return result;
}

//--- ตรวจสอบจำนวน Order ทั้งหมด
int COrderManager::GetTotalOrders()
{
    return PositionsTotal();
}

//--- ตรวจสอบว่ามี Order เปิดอยู่หรือไม่
bool COrderManager::HasOpenOrders()
{
    return GetTotalOrders() > 0;
}

//--- รับผลกำไรปัจจุบัน
double COrderManager::GetCurrentProfit()
{
    double totalProfit = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            totalProfit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    
    return totalProfit;
}

//--- คำนวณขนาด Lot ตามความเสี่ยง
double COrderManager::CalculateLotSize(double riskPercent, double stopLossPips)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * riskPercent / 100;
    double tickValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
    
    if(tickSize > 0 && stopLossPips > 0)
    {
        double calculatedLotSize = riskAmount / (stopLossPips * tickValue / tickSize);
        calculatedLotSize = MathMin(calculatedLotSize, MAX_LOT);
        calculatedLotSize = MathMax(calculatedLotSize, MIN_LOT);
        return NormalizeDouble(calculatedLotSize, 2);
    }
    
    return MIN_LOT;
}

//--- แปลง Pips เป็น Price
double COrderManager::PipsToPrice(double pips)
{
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    // ตรวจสอบว่าเป็น XAUUSD หรือไม่
    if(symbol == "XAUUSD.")
    {
        point = 0.1; // 1 pip = 0.1 สำหรับ XAUUSD
    }
    
    return pips * point;
}

//--- แปลง Price เป็น Pips
double COrderManager::PriceToPips(double price)
{
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    // ตรวจสอบว่าเป็น XAUUSD หรือไม่
    if(symbol == "XAUUSD.")
    {
        point = 0.1; // 1 pip = 0.1 สำหรับ XAUUSD
    }
    
    if(point > 0)
        return price / point;
    return 0;
}

//--- รับ Ticket ของ Order ล่าสุด
ulong COrderManager::GetLastOrderTicket()
{
    if(PositionsTotal() > 0)
    {
        return PositionGetTicket(PositionsTotal() - 1);
    }
    return 0;
}
