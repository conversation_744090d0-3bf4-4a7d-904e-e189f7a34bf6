//+------------------------------------------------------------------+
//|                                                    Constants.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

//--- Range Mode Types
enum ENUM_RANGE_MODE
{
   RANGE_SESSION,      // Session-based range
   RANGE_FIXED         // Fixed time range
};

//--- Zone Types
enum ENUM_ZONE_TYPE
{
   ZONE_POC,          // Point of Control
   ZONE_HVN,          // High Volume Node
   ZONE_LVN,          // Low Volume Node
   ZONE_SUPPORT,      // Support zone (for pairing)
   ZONE_RESISTANCE    // Resistance zone (for pairing)
};

//--- Zone Status Enumeration
enum ENUM_ZONE_STATUS
{
   ZONE_FRESH = 0,        // Fresh zone (not touched)
   ZONE_TESTED = 1,       // Zone has been tested/touched
   ZONE_STALE = 2         // Old zone (should be removed)
};

//--- Filter Types
enum ENUM_FILTER_TYPE
{
   FILTER_VOLATILITY, // ATR-based volatility filter
   FILTER_TREND,      // Trend direction filter
   FILTER_PRICE_ACTION // Price action confirmation filter
};

//--- Constants
#define MAX_ZONES           12
#define MAX_TICKS_PER_CHUNK 100000
#define MIN_TICKS_REQUIRED  1500
#define DEFAULT_SMOOTH_WINDOW 3
#define DEFAULT_HVN_PERCENTILE 80
#define DEFAULT_LVN_PERCENTILE 25

//--- Zone Information Structure
struct ZoneInfo
{
   double            mid_price;        // Zone center price
   double            upper_price;      // Zone upper boundary
   double            lower_price;      // Zone lower boundary
   ENUM_ZONE_TYPE    type;            // Zone type (POC, HVN, LVN)
   ENUM_ZONE_STATUS  status;          // Zone status
   datetime          created_at;      // Creation timestamp
   datetime          touched_at;      // Time when zone was touched
   datetime          last_touch_time; // Last time zone was touched
   double            volume;          // Associated volume
   double            score;           // Zone quality score
   int               touch_count;     // Number of times touched
   int               bin_index;       // Associated bin index
};

//--- Tick Data Structure
struct TickData
{
   datetime          time;            // Tick time
   double            price;           // Calculated price (mid/bid/last)
   double            bid;             // Bid price
   double            ask;             // Ask price
   double            last;            // Last price
   ulong             volume;          // Tick volume
   uint              flags;           // Tick flags
};

//--- Volume Profile Bin
struct VolumeProfileBin
{
   double            price_level;     // Price level for this bin
   ulong             volume;          // Volume at this level
   ulong             volume_smooth;   // Smoothed volume
   int               tick_count;      // Number of ticks
};

//--- Session Time Structure
struct SessionTime
{
   int               start_hour;      // Session start hour
   int               start_minute;    // Session start minute
   int               end_hour;        // Session end hour
   int               end_minute;      // Session end minute
   string            name;            // Session name
};

//--- Filter Parameters
struct FilterParams
{
   bool              use_volatility_filter;    // Enable volatility filter
   bool              use_trend_filter;         // Enable trend filter
   bool              use_price_action_filter;  // Enable price action filter

   // Advanced Volatility Filter Parameters
   double            min_atr_ratio;            // Minimum ATR ratio for volatility filter
   int               atr_lookback_periods;     // Periods for ATR median calculation (default: 50)

   // Advanced Trend Filter Parameters
   int               trend_ma_period;          // Slow MA period for trend filter (default: 50)
   int               trend_ma_fast_period;     // Fast MA period for trend detection (default: 25)
   double            trend_strength_threshold; // Minimum trend strength (default: 0.1)

   // Price Action Filter Parameters
   double            rejection_wick_ratio;     // Minimum wick ratio for price action filter
   double            volume_spike_ratio;       // Minimum volume spike ratio
   int               volume_lookback_periods;  // Periods for volume average calculation (default: 20)
};

//--- Zone Pair Structure
struct ZonePair
{
   ZoneInfo          support_zone;     // Support zone (lower price)
   ZoneInfo          resistance_zone;  // Resistance zone (higher price)
   double            range_height;     // Distance between zones
   double            range_ratio;      // Range height / ATR ratio
   bool              is_valid_pair;    // Whether this pair is valid
   datetime          created_at;       // When this pair was created
   double            pair_score;       // Combined score of both zones
};
