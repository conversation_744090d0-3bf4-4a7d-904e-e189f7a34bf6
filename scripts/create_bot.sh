#!/bin/bash

# TradingBots - สคริปต์สำหรับสร้างบอทใหม่
# ใช้งาน: ./create_bot.sh [BOT_NAME] [BOT_DESCRIPTION] [BOT_AUTHOR]

# ตรวจสอบพารามิเตอร์
if [ $# -lt 3 ]; then
    echo "การใช้งาน: $0 [BOT_NAME] [BOT_DESCRIPTION] [BOT_AUTHOR]"
    echo "ตัวอย่าง: $0 ForexBot 'บอทเทรดฟอเร็กซ์' '<PERSON>'"
    exit 1
fi

BOT_NAME=$1
BOT_DESCRIPTION=$2
BOT_AUTHOR=$3
BOT_VERSION="1.00"
BOT_LINK="https://github.com/tradingbots"

echo "=== สร้างบอทใหม่: $BOT_NAME ==="

# สร้างโฟลเดอร์สำหรับบอท
BOT_DIR="src/bots/$BOT_NAME"
mkdir -p "$BOT_DIR"

echo "สร้างโฟลเดอร์: $BOT_DIR"

# สร้างไฟล์ .mq5 หลัก
cat > "$BOT_DIR/${BOT_NAME}.mq5" << EOF
//+------------------------------------------------------------------+
//| ${BOT_NAME}.mq5 - ${BOT_DESCRIPTION}
//+------------------------------------------------------------------+
#property copyright "${BOT_AUTHOR}"
#property link      "${BOT_LINK}"
#property version   "${BOT_VERSION}"
#property description "${BOT_DESCRIPTION}"

//--- Include files
#include "../../core/includes/Constants.mqh"
#include "../../core/includes/OrderManager.mqh"
#include "../../core/includes/IndicatorManager.mqh"
#include "../../core/classes/PerformanceAnalyzer.mqh"

//--- Input Parameters
input group "=== Risk Management ==="
input double RiskPercent = 2.0;           // ความเสี่ยงต่อการเทรด (เปอร์เซ็นต์)
input double StopLossPips = 50;           // Stop Loss (pips)
input double TakeProfitPips = 100;        // Take Profit (pips)

input group "=== Trading Settings ==="
input bool EnableTrading = true;          // เปิดใช้งานการเทรด
input int MagicNumber = 123456;           // Magic Number
input string TradeComment = "${BOT_NAME}"; // ข้อความใน Order

//--- Global Variables
COrderManager orderManager;               // ตัวจัดการ Order
CIndicatorManager indicatorManager;       // ตัวจัดการ Indicators
CPerformanceAnalyzer performanceAnalyzer; // ตัววิเคราะห์ประสิทธิภาพ

datetime lastBarTime;                     // เวลาของแท่งเทียนล่าสุด
bool isNewBar;                           // ตรวจสอบแท่งเทียนใหม่

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== ${BOT_NAME} Initialization ===");
    
    // เริ่มต้น Order Manager
    orderManager.SetSymbol(Symbol());
    orderManager.SetStopLoss(StopLossPips * _Point);
    orderManager.SetTakeProfit(TakeProfitPips * _Point);
    
    // คำนวณขนาด Lot ตามความเสี่ยง
    double lotSize = orderManager.CalculateLotSize(RiskPercent, StopLossPips);
    orderManager.SetLotSize(lotSize);
    
    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);
    Print("Stop Loss: ", StopLossPips, " pips");
    Print("Take Profit: ", TakeProfitPips, " pips");
    
    // เริ่มต้น Indicator Manager
    if(!indicatorManager.Initialize())
    {
        Print("ข้อผิดพลาด: ไม่สามารถเริ่มต้น Indicators ได้");
        return INIT_FAILED;
    }
    
    // เริ่มต้น Performance Analyzer
    performanceAnalyzer.Initialize();
    
    Print("Indicators เริ่มต้นสำเร็จ");
    
    // ตั้งค่าเวลาเริ่มต้น
    lastBarTime = iTime(Symbol(), Period(), 0);
    
    Print("=== ${BOT_NAME} พร้อมใช้งาน ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== ${BOT_NAME} Deinitialization ===");
    
    // ปิด Indicators
    indicatorManager.Deinitialize();
    
    // ปิด Order ทั้งหมด
    orderManager.CloseAllOrders();
    
    // บันทึกสถิติประสิทธิภาพ
    performanceAnalyzer.SaveStatistics();
    
    Print("${BOT_NAME} ถูกปิดแล้ว");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // ตรวจสอบแท่งเทียนใหม่
    if(!IsNewBar())
        return;
    
    // ตรวจสอบการเปิดใช้งานการเทรด
    if(!EnableTrading)
        return;
    
    // ตรวจสอบ Order ที่เปิดอยู่
    if(orderManager.HasOpenOrders())
        return;
    
    // วิเคราะห์สัญญาณการเทรด
    int signal = AnalyzeTradingSignal();
    
    // ดำเนินการตามสัญญาณ
    if(signal == 1) // สัญญาณ Buy
    {
        if(orderManager.OpenBuyOrder())
        {
            Print("เปิด Order Buy สำเร็จ");
            performanceAnalyzer.RecordTrade(ORDER_TYPE_BUY, Ask, StopLossPips, TakeProfitPips);
        }
    }
    else if(signal == -1) // สัญญาณ Sell
    {
        if(orderManager.OpenSellOrder())
        {
            Print("เปิด Order Sell สำเร็จ");
            performanceAnalyzer.RecordTrade(ORDER_TYPE_SELL, Bid, StopLossPips, TakeProfitPips);
        }
    }
}

//+------------------------------------------------------------------+
//| ตรวจสอบแท่งเทียนใหม่                                            |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        isNewBar = true;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| วิเคราะห์สัญญาณการเทรด                                          |
//+------------------------------------------------------------------+
int AnalyzeTradingSignal()
{
    // TODO: เพิ่มตรรกะการวิเคราะห์สัญญาณตามกลยุทธ์ที่ต้องการ
    
    // ตัวอย่าง: ใช้ RSI และ MACD
    double rsi = indicatorManager.GetRSI(14, 1);
    double macd = indicatorManager.GetMACD(12, 26, 9, 1);
    double macdSignal = indicatorManager.GetMACDSignal(12, 26, 9, 1);
    
    // สัญญาณ Buy: RSI oversold + MACD bullish
    if(rsi < 30 && macd > macdSignal)
    {
        return 1; // สัญญาณ Buy
    }
    
    // สัญญาณ Sell: RSI overbought + MACD bearish
    if(rsi > 70 && macd < macdSignal)
    {
        return -1; // สัญญาณ Sell
    }
    
    return 0; // ไม่มีสัญญาณ
}
EOF

echo "สร้างไฟล์: ${BOT_NAME}.mq5"

# สร้างไฟล์ config
cat > "config/bots/${BOT_NAME}.conf" << EOF
# ${BOT_NAME} Configuration File
# ไฟล์การตั้งค่าสำหรับ ${BOT_NAME}

# === Bot Information ===
BOT_NAME=${BOT_NAME}
BOT_VERSION=${BOT_VERSION}
BOT_DESCRIPTION=${BOT_DESCRIPTION}
BOT_AUTHOR=${BOT_AUTHOR}
BOT_SYMBOL=EURUSD
BOT_TIMEFRAMES=M15,H1,H4

# === Risk Management ===
RISK_PERCENT=2.0
STOP_LOSS_PIPS=50
TAKE_PROFIT_PIPS=100
MAX_SPREAD_PIPS=5
MAX_ORDERS=1

# === Trading Settings ===
ENABLE_TRADING=true
MAGIC_NUMBER=123456
TRADE_COMMENT=${BOT_NAME}
TRADING_START_HOUR=8
TRADING_END_HOUR=22
TRADING_DAYS=MON,TUE,WED,THU,FRI

# === Lot Size Settings ===
MIN_LOT=0.01
MAX_LOT=10.0

# === Performance Settings ===
ENABLE_PERFORMANCE_ANALYSIS=true
SAVE_STATISTICS=true
LOG_LEVEL=INFO

# === Backtest Settings ===
BACKTEST_START_DATE=2024-01-01
BACKTEST_END_DATE=2024-12-31
BACKTEST_INITIAL_DEPOSIT=10000
BACKTEST_CURRENCY=USD
EOF

echo "สร้างไฟล์ config: config/bots/${BOT_NAME}.conf"

# สร้างไฟล์ README สำหรับบอท
cat > "$BOT_DIR/README.md" << EOF
# ${BOT_NAME}

## รายละเอียด

${BOT_DESCRIPTION}

## คุณสมบัติ

- ใช้กลยุทธ์ RSI + MACD
- จัดการความเสี่ยงอัตโนมัติ
- วิเคราะห์ประสิทธิภาพ
- รองรับการ backtest

## การติดตั้ง

1. คัดลอกไฟล์ \`${BOT_NAME}.mq5\` ไปยังโฟลเดอร์ \`MQL5/Experts/\`
2. รีสตาร์ท MT5
3. ลาก EA ไปยังชาร์ตที่ต้องการ

## การตั้งค่า

แก้ไขไฟล์ \`config/bots/${BOT_NAME}.conf\` หรือตั้งค่าใน MT5

## การใช้งาน

1. เปิดชาร์ตใน MT5
2. ลาก EA ไปยังชาร์ต
3. ตั้งค่าพารามิเตอร์
4. เปิดใช้งาน AutoTrading

## ผู้พัฒนา

${BOT_AUTHOR}

## เวอร์ชัน

${BOT_VERSION}
EOF

echo "สร้างไฟล์ README: $BOT_DIR/README.md"

# สร้างไฟล์ test
cat > "tests/${BOT_NAME}_test.mq5" << EOF
//+------------------------------------------------------------------+
//| ${BOT_NAME} Test File
//+------------------------------------------------------------------+
#property copyright "${BOT_AUTHOR}"
#property version   "${BOT_VERSION}"

//--- Test functions
void TestIndicators()
{
    Print("ทดสอบ Indicators สำหรับ ${BOT_NAME}");
    // TODO: เพิ่มการทดสอบ
}

void TestRiskManagement()
{
    Print("ทดสอบการจัดการความเสี่ยงสำหรับ ${BOT_NAME}");
    // TODO: เพิ่มการทดสอบ
}

void TestOrderManagement()
{
    Print("ทดสอบการจัดการ Order สำหรับ ${BOT_NAME}");
    // TODO: เพิ่มการทดสอบ
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== เริ่มการทดสอบ ${BOT_NAME} ===");
    
    TestIndicators();
    TestRiskManagement();
    TestOrderManagement();
    
    Print("=== การทดสอบ ${BOT_NAME} เสร็จสิ้น ===");
}
EOF

echo "สร้างไฟล์ test: tests/${BOT_NAME}_test.mq5"

echo ""
echo "=== สร้างบอท $BOT_NAME เสร็จสิ้น ==="
echo "ไฟล์ที่สร้าง:"
echo "  - src/bots/$BOT_NAME/${BOT_NAME}.mq5"
echo "  - config/bots/${BOT_NAME}.conf"
echo "  - src/bots/$BOT_NAME/README.md"
echo "  - tests/${BOT_NAME}_test.mq5"
echo ""
echo "ขั้นตอนต่อไป:"
echo "1. แก้ไขไฟล์ ${BOT_NAME}.mq5 ตามกลยุทธ์ที่ต้องการ"
echo "2. ตั้งค่าพารามิเตอร์ในไฟล์ config"
echo "3. ทดสอบบอทด้วย backtest"
echo "4. คอมไพล์และใช้งานจริง"
