//+------------------------------------------------------------------+
//|                                            CounterAttack_Test.mq5 |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

// Simple test EA to verify compilation
input int TestInput = 10;

datetime g_lastTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Test EA initialized");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Test EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime current = TimeCurrent();
   if(current != g_lastTime)
   {
      g_lastTime = current;
      double atr = iATR(Symbol(), PERIOD_H1, 14, 1);
      Print("ATR: ", atr);
   }
}
