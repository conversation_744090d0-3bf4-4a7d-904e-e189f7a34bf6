//+------------------------------------------------------------------+
//|                                                ScoringSystem.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Scoring System Class                                             |
//+------------------------------------------------------------------+
class CScoringSystem
{
private:
   string            m_symbol;                 // Symbol name
   int               m_max_zones;              // Maximum zones to output
   
   // Scoring weights
   double            m_prominence_weight;      // Volume prominence weight
   double            m_proximity_weight;       // Price proximity weight
   double            m_type_weight;           // Zone type weight
   double            m_fresh_bonus;           // Fresh zone bonus
   
   // Helper methods
   double            CalculateProminence(const ZoneInfo &zone, double max_volume);
   double            CalculateProximity(const ZoneInfo &zone, double current_price, double atr);
   double            CalculateTypeBonus(const ZoneInfo &zone);
   double            CalculateFreshBonus(const ZoneInfo &zone);

public:
   // Constructor/Destructor
                     CScoringSystem(string symbol = "", int max_zones = MAX_ZONES);
                    ~CScoringSystem();
   
   // Configuration
   void              SetMaxZones(int max_zones) { m_max_zones = max_zones; }
   void              SetScoringWeights(double prominence, double proximity, double type, double fresh);
   
   // Scoring methods
   double            CalculateZoneScore(const ZoneInfo &zone, double max_volume, 
                                      double current_price, double atr);
   bool              ScoreZones(ZoneInfo &zones[], int zone_count);
   bool              RankAndLimitZones(ZoneInfo &zones[], int &zone_count);
   
   // Zone pair methods
   bool              ScoreZonePairs(ZonePair &pairs[], int pair_count);
   bool              OutputZonePairs(const ZonePair &pairs[], int pair_count);
   bool              DrawZonePairOnChart(const ZonePair &pair, int index);
   
   // Utility methods
   void              PrintScoringWeights() const;
};

//+------------------------------------------------------------------+
//| Output System Class                                              |
//+------------------------------------------------------------------+
class COutputSystem
{
private:
   string            m_symbol;                 // Symbol name
   string            m_timeframe;              // Timeframe string
   
   // Indicator buffers (if using indicator output)
   double            m_mid_buffer[];           // Mid price buffer
   double            m_upper_buffer[];         // Upper boundary buffer
   double            m_lower_buffer[];         // Lower boundary buffer
   double            m_type_buffer[];          // Zone type buffer
   double            m_status_buffer[];        // Zone status buffer
   double            m_score_buffer[];         // Zone score buffer
   
   // File output
   bool              m_use_file_output;        // Enable file output
   string            m_output_filename;        // Output filename
   
   // Chart objects
   bool              m_draw_zones;             // Draw zones on chart
   string            m_object_prefix;          // Object name prefix

public:
   // Constructor/Destructor
                     COutputSystem(string symbol = "", string timeframe = "");
                    ~COutputSystem();
   
   // Configuration
   void              SetFileOutput(bool enable, string filename = "");
   void              SetChartDrawing(bool enable, string prefix = "CounterAttack_");
   
   // Output methods
   bool              OutputToBuffers(const ZoneInfo &zones[], int zone_count);
   bool              OutputToFile(const ZoneInfo &zones[], int zone_count);
   bool              OutputToChart(const ZoneInfo &zones[], int zone_count);
   
   // Combined output
   bool              OutputZones(const ZoneInfo &zones[], int zone_count);
   
   // Cleanup
   void              ClearChartObjects();
   
   // Utility methods
   string            GenerateOutputFilename() const;
   void              PrintOutputSummary(const ZoneInfo &zones[], int zone_count) const;
};

//+------------------------------------------------------------------+
//| Scoring System Constructor                                       |
//+------------------------------------------------------------------+
CScoringSystem::CScoringSystem(string symbol = "", int max_zones = MAX_ZONES)
{
   m_symbol = (symbol == "") ? ChartSymbol() : symbol;
   m_max_zones = max_zones;
   
   // Default scoring weights
   m_prominence_weight = 0.6;   // 60% for volume prominence
   m_proximity_weight = 0.3;    // 30% for price proximity
   m_type_weight = 0.1;         // 10% for zone type
   m_fresh_bonus = 0.5;         // +0.5 bonus for fresh zones
}

//+------------------------------------------------------------------+
//| Scoring System Destructor                                       |
//+------------------------------------------------------------------+
CScoringSystem::~CScoringSystem()
{
   // Nothing to clean up
}

//+------------------------------------------------------------------+
//| Set scoring weights                                              |
//+------------------------------------------------------------------+
void CScoringSystem::SetScoringWeights(double prominence, double proximity, double type, double fresh)
{
   m_prominence_weight = prominence;
   m_proximity_weight = proximity;
   m_type_weight = type;
   m_fresh_bonus = fresh;
}

//+------------------------------------------------------------------+
//| Calculate volume prominence score (0-1)                         |
//+------------------------------------------------------------------+
double CScoringSystem::CalculateProminence(const ZoneInfo &zone, double max_volume)
{
   if(max_volume <= 0)
      return 0;
   
   return zone.volume / max_volume;
}

//+------------------------------------------------------------------+
//| Calculate price proximity score (0-2)                           |
//+------------------------------------------------------------------+
double CScoringSystem::CalculateProximity(const ZoneInfo &zone, double current_price, double atr)
{
   if(atr <= 0)
      return 0;
   
   double distance = MathAbs(current_price - zone.mid_price);
   double proximity = 1.0 / (distance / atr + 0.5);
   
   return MathMax(0, MathMin(2.0, proximity));
}

//+------------------------------------------------------------------+
//| Calculate zone type bonus                                        |
//+------------------------------------------------------------------+
double CScoringSystem::CalculateTypeBonus(const ZoneInfo &zone)
{
   switch(zone.type)
   {
      case ZONE_POC: return 1.3;  // POC gets highest bonus
      case ZONE_LVN: return 1.2;  // LVN typically provides stronger reactions
      case ZONE_HVN: return 1.0;  // HVN baseline
      default: return 1.0;
   }
}

//+------------------------------------------------------------------+
//| Calculate fresh zone bonus                                       |
//+------------------------------------------------------------------+
double CScoringSystem::CalculateFreshBonus(const ZoneInfo &zone)
{
   return (zone.status == ZONE_FRESH) ? m_fresh_bonus : 0.0;
}

//+------------------------------------------------------------------+
//| Calculate overall zone score (0-10 scale)                       |
//+------------------------------------------------------------------+
double CScoringSystem::CalculateZoneScore(const ZoneInfo &zone, double max_volume, 
                                         double current_price, double atr)
{
   double prominence = CalculateProminence(zone, max_volume);
   double proximity = CalculateProximity(zone, current_price, atr);
   double type_bonus = CalculateTypeBonus(zone);
   double fresh_bonus = CalculateFreshBonus(zone);
   
   double base_score = m_prominence_weight * prominence + 
                      m_proximity_weight * proximity + 
                      m_type_weight * type_bonus;
   
   double final_score = base_score + fresh_bonus;
   
   // Scale to 0-10 range
   return MathMax(0, MathMin(10.0, final_score * 10.0));
}

//+------------------------------------------------------------------+
//| Score all zones                                                  |
//+------------------------------------------------------------------+
bool CScoringSystem::ScoreZones(ZoneInfo &zones[], int zone_count)
{
   if(zone_count <= 0)
      return false;
   
   // Find maximum volume for normalization
   double max_volume = 0;
   for(int i = 0; i < zone_count; i++)
   {
      if(zones[i].volume > max_volume)
         max_volume = zones[i].volume;
   }
   
   // Get current price and ATR
   double current_price = iClose(m_symbol, PERIOD_H1, 0);
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default value
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      atr = atr_values[0];
   }
   IndicatorRelease(atr_handle);
   
   // Calculate scores
   for(int i = 0; i < zone_count; i++)
   {
      zones[i].score = CalculateZoneScore(zones[i], max_volume, current_price, atr);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Rank zones by score and limit to max count                      |
//+------------------------------------------------------------------+
bool CScoringSystem::RankAndLimitZones(ZoneInfo &zones[], int &zone_count)
{
   if(zone_count <= 0)
      return false;
   
   // Sort zones by score (descending)
   for(int i = 0; i < zone_count - 1; i++)
   {
      for(int j = i + 1; j < zone_count; j++)
      {
         if(zones[j].score > zones[i].score)
         {
            ZoneInfo temp = zones[i];
            zones[i] = zones[j];
            zones[j] = temp;
         }
      }
   }
   
   // Limit to max zones
   if(zone_count > m_max_zones)
   {
      zone_count = m_max_zones;
      ArrayResize(zones, zone_count);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Score zone pairs                                                 |
//+------------------------------------------------------------------+
bool CScoringSystem::ScoreZonePairs(ZonePair &pairs[], int pair_count)
{
   if(pair_count <= 0)
      return false;
   
   double current_price = iClose(m_symbol, PERIOD_H1, 0);
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default value
   
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      atr = atr_values[0];
   }
   IndicatorRelease(atr_handle);
   
   for(int i = 0; i < pair_count; i++)
   {
      // Calculate pair score based on individual zone scores
      double support_score = pairs[i].support_zone.score;
      double resistance_score = pairs[i].resistance_zone.score;
      
      // Base pair score is average of individual scores
      pairs[i].pair_score = (support_score + resistance_score) / 2.0;
      
      // Bonus for good range ratio (1.0 - 3.0 ATR is ideal)
      if(pairs[i].range_ratio >= 1.0 && pairs[i].range_ratio <= 3.0)
      {
         pairs[i].pair_score += 1.0;
      }
      
      // Bonus for balanced scores (both zones should be strong)
      double score_difference = MathAbs(support_score - resistance_score);
      if(score_difference < 2.0) // Both zones have similar strength
      {
         pairs[i].pair_score += 0.5;
      }
      
      // Bonus for current price position (prefer pairs near current price)
      double mid_pair_price = (pairs[i].support_zone.mid_price + pairs[i].resistance_zone.mid_price) / 2.0;
      double price_distance = MathAbs(current_price - mid_pair_price) / atr;
      if(price_distance < 2.0) // Current price is near the pair
      {
         pairs[i].pair_score += 0.3;
      }
   }
   
   Print("Scored ", pair_count, " zone pairs");
   return true;
}

//+------------------------------------------------------------------+
//| Output zone pairs                                                |
//+------------------------------------------------------------------+
bool CScoringSystem::OutputZonePairs(const ZonePair &pairs[], int pair_count)
{
   if(pair_count <= 0)
      return false;
   
   Print("=== Zone Pairs Output ===");
   
   for(int i = 0; i < pair_count; i++)
   {
      Print("Pair ", i + 1, ":");
      Print("  Support: ", DoubleToString(pairs[i].support_zone.mid_price, 2), 
            " (Score: ", DoubleToString(pairs[i].support_zone.score, 2), ")");
      Print("  Resistance: ", DoubleToString(pairs[i].resistance_zone.mid_price, 2), 
            " (Score: ", DoubleToString(pairs[i].resistance_zone.score, 2), ")");
      Print("  Range Height: ", DoubleToString(pairs[i].range_height, 2));
      Print("  Range Ratio: ", DoubleToString(pairs[i].range_ratio, 2));
      Print("  Pair Score: ", DoubleToString(pairs[i].pair_score, 2));
      Print("  ---");
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Draw zone pair on chart                                          |
//+------------------------------------------------------------------+
bool CScoringSystem::DrawZonePairOnChart(const ZonePair &pair, int index)
{
   string pair_name = "ZonePair_" + IntegerToString(index);
   string support_name = pair_name + "_Support";
   string resistance_name = pair_name + "_Resistance";
   string label_name = pair_name + "_Label";
   
   // Draw support zone
   ObjectCreate(0, support_name, OBJ_RECTANGLE, 0, 
                pair.created_at, pair.support_zone.lower_price,
                pair.created_at + 86400, pair.support_zone.upper_price);
   
   ObjectSetInteger(0, support_name, OBJPROP_COLOR, clrGreen);
   ObjectSetInteger(0, support_name, OBJPROP_FILL, true);
   ObjectSetInteger(0, support_name, OBJPROP_BACK, true);
   
   // Draw resistance zone
   ObjectCreate(0, resistance_name, OBJ_RECTANGLE, 0,
                pair.created_at, pair.resistance_zone.lower_price,
                pair.created_at + 86400, pair.resistance_zone.upper_price);
   
   ObjectSetInteger(0, resistance_name, OBJPROP_COLOR, clrRed);
   ObjectSetInteger(0, resistance_name, OBJPROP_FILL, true);
   ObjectSetInteger(0, resistance_name, OBJPROP_BACK, true);
   
   // Draw label
   string label_text = "Pair " + IntegerToString(index + 1) + " (Score: " + 
                      DoubleToString(pair.pair_score, 1) + ")";
   
   ObjectCreate(0, label_name, OBJ_TEXT, 0, 
                pair.created_at, pair.resistance_zone.upper_price + 0.5);
   
   ObjectSetString(0, label_name, OBJPROP_TEXT, label_text);
   ObjectSetInteger(0, label_name, OBJPROP_COLOR, clrYellow);
   ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 8);
   
   return true;
}

//+------------------------------------------------------------------+
//| Output System Constructor                                        |
//+------------------------------------------------------------------+
COutputSystem::COutputSystem(string symbol = "", string timeframe = "")
{
   m_symbol = (symbol == "") ? ChartSymbol() : symbol;
   m_timeframe = (timeframe == "") ? EnumToString(ChartPeriod()) : timeframe;
   
   m_use_file_output = true;
   m_draw_zones = true;
   m_object_prefix = "CounterAttack_";
   m_output_filename = GenerateOutputFilename();
   
   // Initialize buffers
   ArrayResize(m_mid_buffer, 0);
   ArrayResize(m_upper_buffer, 0);
   ArrayResize(m_lower_buffer, 0);
   ArrayResize(m_type_buffer, 0);
   ArrayResize(m_status_buffer, 0);
   ArrayResize(m_score_buffer, 0);
}

//+------------------------------------------------------------------+
//| Output System Destructor                                        |
//+------------------------------------------------------------------+
COutputSystem::~COutputSystem()
{
   ClearChartObjects();
   ArrayFree(m_mid_buffer);
   ArrayFree(m_upper_buffer);
   ArrayFree(m_lower_buffer);
   ArrayFree(m_type_buffer);
   ArrayFree(m_status_buffer);
   ArrayFree(m_score_buffer);
}

// Removed duplicate function

//+------------------------------------------------------------------+
//| Output zones to file                                             |
//+------------------------------------------------------------------+
bool COutputSystem::OutputToFile(const ZoneInfo &zones[], int zone_count)
{
   if(!m_use_file_output || zone_count <= 0)
      return false;
   
   int file_handle = FileOpen(m_output_filename, FILE_WRITE | FILE_CSV);
   if(file_handle == INVALID_HANDLE)
   {
      Print("Error: Cannot create output file: ", m_output_filename);
      return false;
   }
   
   // Write header
   FileWrite(file_handle, "timestamp", "type", "mid_price", "upper_price", 
             "lower_price", "status", "score", "volume", "created_at");
   
   // Write zone data
   datetime current_time = TimeCurrent();
   for(int i = 0; i < zone_count; i++)
   {
      FileWrite(file_handle, 
                TimeToString(current_time),
                EnumToString(zones[i].type),
                DoubleToString(zones[i].mid_price, 5),
                DoubleToString(zones[i].upper_price, 5),
                DoubleToString(zones[i].lower_price, 5),
                EnumToString(zones[i].status),
                DoubleToString(zones[i].score, 2),
                DoubleToString(zones[i].volume, 0),
                TimeToString(zones[i].created_at));
   }
   
   FileClose(file_handle);
   Print("Zones exported to file: ", m_output_filename);
   
   return true;
}

//+------------------------------------------------------------------+
//| Output zones to chart objects                                    |
//+------------------------------------------------------------------+
bool COutputSystem::OutputToChart(const ZoneInfo &zones[], int zone_count)
{
   if(!m_draw_zones || zone_count <= 0)
      return false;
   
   // Clear existing objects
   ClearChartObjects();
   
   datetime current_time = TimeCurrent();
   datetime future_time = current_time + 3600 * 24; // 24 hours ahead
   
   for(int i = 0; i < zone_count; i++)
   {
      string zone_name = m_object_prefix + "Zone_" + IntegerToString(i);
      string label_name = m_object_prefix + "Label_" + IntegerToString(i);
      
      // Create rectangle
      ObjectCreate(0, zone_name, OBJ_RECTANGLE, 0, 
                   current_time, zones[i].upper_price,
                   future_time, zones[i].lower_price);
      
      // Set rectangle properties
      color zone_color;
      switch(zones[i].type)
      {
         case ZONE_POC: zone_color = clrYellow; break;
         case ZONE_HVN: zone_color = clrLightBlue; break;
         case ZONE_LVN: zone_color = clrLightPink; break;
         default: zone_color = clrGray; break;
      }
      
      ObjectSetInteger(0, zone_name, OBJPROP_COLOR, zone_color);
      ObjectSetInteger(0, zone_name, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(0, zone_name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, zone_name, OBJPROP_FILL, true);
      ObjectSetInteger(0, zone_name, OBJPROP_BACK, true);
      
      // Create label
      string label_text = EnumToString(zones[i].type) + " (" + 
                         DoubleToString(zones[i].score, 1) + ")";
      
      ObjectCreate(0, label_name, OBJ_TEXT, 0, current_time, zones[i].mid_price);
      ObjectSetString(0, label_name, OBJPROP_TEXT, label_text);
      ObjectSetInteger(0, label_name, OBJPROP_COLOR, clrWhite);
      ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 8);
   }
   
   ChartRedraw();
   Print("Zones drawn on chart: ", zone_count, " zones");
   
   return true;
}

//+------------------------------------------------------------------+
//| Set file output configuration                                    |
//+------------------------------------------------------------------+
void COutputSystem::SetFileOutput(bool enable, string filename = "")
{
   m_use_file_output = enable;
   if(filename != "")
      m_output_filename = filename;
   else
      m_output_filename = GenerateOutputFilename();
   
   Print("File output ", (enable ? "enabled" : "disabled"), 
         " | Filename: ", m_output_filename);
}

//+------------------------------------------------------------------+
//| Set chart drawing configuration                                  |
//+------------------------------------------------------------------+
void COutputSystem::SetChartDrawing(bool enable, string prefix = "CounterAttack_")
{
   m_draw_zones = enable;
   m_object_prefix = prefix;
   
   Print("Chart drawing ", (enable ? "enabled" : "disabled"), 
         " | Prefix: ", m_object_prefix);
}

//+------------------------------------------------------------------+
//| Generate output filename                                         |
//+------------------------------------------------------------------+
string COutputSystem::GenerateOutputFilename() const
{
   string filename = "CounterAttack_" + m_symbol + "_" + m_timeframe + "_";
   filename += TimeToString(TimeCurrent(), TIME_DATE);
   filename += ".csv";
   return filename;
}

//+------------------------------------------------------------------+
//| Combined output method                                           |
//+------------------------------------------------------------------+
bool COutputSystem::OutputZones(const ZoneInfo &zones[], int zone_count)
{
   bool success = true;
   
   if(m_use_file_output)
      success &= OutputToFile(zones, zone_count);
   
   if(m_draw_zones)
      success &= OutputToChart(zones, zone_count);
   
   PrintOutputSummary(zones, zone_count);
   
   return success;
}

//+------------------------------------------------------------------+
//| Clear chart objects                                              |
//+------------------------------------------------------------------+
void COutputSystem::ClearChartObjects()
{
   int total_objects = ObjectsTotal(0);
   
   for(int i = total_objects - 1; i >= 0; i--)
   {
      string obj_name = ObjectName(0, i);
      if(StringFind(obj_name, m_object_prefix) == 0)
      {
         ObjectDelete(0, obj_name);
      }
   }
}

//+------------------------------------------------------------------+
//| Print output summary                                             |
//+------------------------------------------------------------------+
void COutputSystem::PrintOutputSummary(const ZoneInfo &zones[], int zone_count) const
{
   Print("=== Output Summary ===");
   Print("Total zones output: ", zone_count);
   
   for(int i = 0; i < zone_count; i++)
   {
      Print("Zone ", i + 1, ": ", EnumToString(zones[i].type),
            " | Price: ", DoubleToString(zones[i].mid_price, 5),
            " | Score: ", DoubleToString(zones[i].score, 2),
            " | Status: ", EnumToString(zones[i].status));
   }
}
