# Changelog

การเปลี่ยนแปลงทั้งหมดในโปรเจค TradingBots จะถูกบันทึกในไฟล์นี้

รูปแบบการบันทึกขึ้นอยู่กับ [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
และโปรเจคนี้ใช้ [Semantic Versioning](https://semver.org/spec/v2.0.0.html)

## [Unreleased]

### Added

- โครงสร้างโปรเจคใหม่ที่รองรับการทำงานเป็นทีม
- ระบบการสร้างบอทใหม่ด้วยสคริปต์
- การจัดการ config แยกส่วน
- เทมเพลตสำหรับสร้างบอทใหม่
- ระบบการทดสอบที่ครอบคลุม

### Changed

- ปรับปรุงโครงสร้างโฟลเดอร์ให้เหมาะสมสำหรับการทำงานเป็นทีม
- ย้ายไฟล์ core ไปยังโฟลเดอร์ที่เหมาะสม
- ปรับปรุง README.md ให้ครอบคลุมมากขึ้น

### Removed

- ไฟล์ main.mq5 เดิม (ย้ายไปเป็น GoldBot)
- โครงสร้างโฟลเดอร์เก่าที่ไม่เหมาะสม

## [1.0.0] - 2024-12-19

### Added

- โครงสร้างโปรเจคพื้นฐาน
- GoldBot สำหรับเทรดทองคำ
- ระบบจัดการ Order และ Indicators
- ระบบวิเคราะห์ประสิทธิภาพ
- การจัดการความเสี่ยงอัตโนมัติ

### Changed

- ปรับปรุงโค้ดให้เหมาะสมสำหรับการใช้งานจริง
- เพิ่มการจัดการข้อผิดพลาด
- ปรับปรุงการ logging

## [0.1.0] - 2024-12-01

### Added

- โครงสร้างโปรเจคเริ่มต้น
- EA พื้นฐานสำหรับเทรดทองคำ
- ระบบ Indicators พื้นฐาน
