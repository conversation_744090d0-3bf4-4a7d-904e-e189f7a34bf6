//+------------------------------------------------------------------+
//|                                                  ZoneManager.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Zone Manager Class (Builder + Freshness Checker)                |
//+------------------------------------------------------------------+
class CZoneManager
{
private:
   ZoneInfo          m_zones[];                // Active zones array
   ZonePair          m_zone_pairs[];           // Zone pairs (support + resistance)
   int               m_zone_count;             // Current zone count
   int               m_pair_count;             // Current pair count
   string            m_symbol;                 // Symbol name
   double            m_zone_width_multiplier;  // Zone width multiplier
   double            m_touch_tolerance;        // Touch tolerance
   int               m_max_zones;              // Maximum zones to keep
   int               m_max_pairs;              // Maximum pairs to keep
   
   // Helper methods
   double            CalculateZoneWidth(double atr_value);
   bool              IsZoneTouched(const ZoneInfo &zone, double high, double low, double close);
   void              SortZonesByScore();
   bool              MergeNearbyZones(double min_distance);
   
   // New methods for zone pairing
   bool              CreateZonePairs();
   bool              IsValidZonePair(const ZoneInfo &zone1, const ZoneInfo &zone2);
   void              CalculatePairMetrics(ZonePair &pair);
   bool              FilterValidZonePairs();
   void              SortPairsByScore();
   double            GetCurrentATR();                    // Get current ATR value

public:
   // Constructor/Destructor
                     CZoneManager(string symbol = "");
                    ~CZoneManager();
   
   // Configuration
   void              SetZoneWidthMultiplier(double multiplier) { m_zone_width_multiplier = multiplier; }
   void              SetTouchTolerance(double tolerance) { m_touch_tolerance = tolerance; }
   void              SetMaxZones(int max_zones) { m_max_zones = max_zones; }
   void              SetMaxZonePairs(int max_pairs) { m_max_pairs = max_pairs; }
   void              SetUseZonePairing(bool use_pairing) { /* Future use */ }
   
   // Zone creation
   bool              CreateZonesFromNodes(const int poc_index, const int &hvn_indices[], int hvn_count,
                                        const int &lvn_indices[], int lvn_count,
                                        const VolumeProfileBin &bins[], int bin_count,
                                        datetime created_time);
   
   // Freshness checking
   bool              UpdateFreshness(datetime current_time);
   bool              CheckZonesTouched(datetime from_time, datetime to_time);
   
   // Zone management
   void              ClearZones();
   bool              RemoveStaleZones(int max_age_hours = 168); // 1 week default
   
   // Data access
   int               GetZoneCount() const { return m_zone_count; }
   int               GetPairCount() const { return m_pair_count; }
   bool              GetZone(int index, ZoneInfo &zone) const;
   bool              GetZonePair(int index, ZonePair &pair) const;
   bool              GetFreshZones(ZoneInfo &fresh_zones[], int &count) const;
   bool              GetFreshZonePairs(ZonePair &fresh_pairs[], int &count) const;
   bool              GetZonesByType(ENUM_ZONE_TYPE type, ZoneInfo &zones[], int &count) const;
   
   // Utility methods
   void              PrintZones() const;
   bool              SaveZonesToFile(string filename) const;
   bool              LoadZonesFromFile(string filename);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CZoneManager::CZoneManager(string symbol = "")
{
   m_symbol = (symbol == "") ? ChartSymbol() : symbol;
   m_zone_count = 0;
   m_pair_count = 0;
   m_zone_width_multiplier = 0.25; // 25% of ATR
   m_touch_tolerance = 0.25;       // 25% of ATR
   m_max_zones = MAX_ZONES;
   m_max_pairs = MAX_ZONES / 2;    // Maximum pairs is half of max zones
   ArrayResize(m_zones, 0);
   ArrayResize(m_zone_pairs, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CZoneManager::~CZoneManager()
{
   ArrayFree(m_zones);
}

//+------------------------------------------------------------------+
//| Calculate zone width based on ATR                                |
//+------------------------------------------------------------------+
double CZoneManager::CalculateZoneWidth(double atr_value)
{
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double min_width = 2.0 * 10 * point; // Minimum 2 * BinSizePoints
   double atr_width = atr_value * m_zone_width_multiplier;
   
   return MathMax(min_width, atr_width);
}

//+------------------------------------------------------------------+
//| Create zones from detected nodes                                 |
//+------------------------------------------------------------------+
bool CZoneManager::CreateZonesFromNodes(const int poc_index, const int &hvn_indices[], int hvn_count,
                                       const int &lvn_indices[], int lvn_count,
                                       const VolumeProfileBin &bins[], int bin_count,
                                       datetime created_time)
{
   ClearZones();
   
   // Get ATR for zone width calculation
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default value
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      atr = atr_values[0];
   }
   IndicatorRelease(atr_handle);
   double zone_width = CalculateZoneWidth(atr);
   
   int total_nodes = 1 + hvn_count + lvn_count; // POC + HVN + LVN
   ArrayResize(m_zones, total_nodes);
   
   // Create POC zone
   if(poc_index >= 0 && poc_index < bin_count)
   {
      m_zones[m_zone_count].mid_price = bins[poc_index].price_level;
      m_zones[m_zone_count].upper_price = m_zones[m_zone_count].mid_price + zone_width / 2.0;
      m_zones[m_zone_count].lower_price = m_zones[m_zone_count].mid_price - zone_width / 2.0;
      m_zones[m_zone_count].type = ZONE_POC;
      m_zones[m_zone_count].status = ZONE_FRESH;
      m_zones[m_zone_count].created_at = created_time;
      m_zones[m_zone_count].touched_at = 0;
      m_zones[m_zone_count].volume = (double)bins[poc_index].volume_smooth;
      m_zones[m_zone_count].bin_index = poc_index;
      m_zones[m_zone_count].score = 0; // Will be calculated later
      m_zone_count++;
   }
   
   // Create HVN zones
   for(int i = 0; i < hvn_count; i++)
   {
      int idx = hvn_indices[i];
      if(idx >= 0 && idx < bin_count && idx != poc_index)
      {
         m_zones[m_zone_count].mid_price = bins[idx].price_level;
         m_zones[m_zone_count].upper_price = m_zones[m_zone_count].mid_price + zone_width / 2.0;
         m_zones[m_zone_count].lower_price = m_zones[m_zone_count].mid_price - zone_width / 2.0;
         m_zones[m_zone_count].type = ZONE_HVN;
         m_zones[m_zone_count].status = ZONE_FRESH;
         m_zones[m_zone_count].created_at = created_time;
         m_zones[m_zone_count].touched_at = 0;
         m_zones[m_zone_count].volume = (double)bins[idx].volume_smooth;
         m_zones[m_zone_count].bin_index = idx;
         m_zones[m_zone_count].score = 0;
         m_zone_count++;
      }
   }
   
   // Create LVN zones
   for(int i = 0; i < lvn_count; i++)
   {
      int idx = lvn_indices[i];
      if(idx >= 0 && idx < bin_count)
      {
         m_zones[m_zone_count].mid_price = bins[idx].price_level;
         m_zones[m_zone_count].upper_price = m_zones[m_zone_count].mid_price + zone_width / 2.0;
         m_zones[m_zone_count].lower_price = m_zones[m_zone_count].mid_price - zone_width / 2.0;
         m_zones[m_zone_count].type = ZONE_LVN;
         m_zones[m_zone_count].status = ZONE_FRESH;
         m_zones[m_zone_count].created_at = created_time;
         m_zones[m_zone_count].touched_at = 0;
         m_zones[m_zone_count].volume = (double)bins[idx].volume_smooth;
         m_zones[m_zone_count].bin_index = idx;
         m_zones[m_zone_count].score = 0;
         m_zone_count++;
      }
   }
   
   // Merge nearby zones
   double min_distance = atr * 0.25;
   MergeNearbyZones(min_distance);
   
   // Create zone pairs (support + resistance)
   if(CreateZonePairs())
   {
      FilterValidZonePairs();
      Print("Zone pairs created and filtered successfully");
   }
   
   Print("Created ", m_zone_count, " zones (POC: 1, HVN: ", hvn_count, ", LVN: ", lvn_count, ")");
   Print("Created ", m_pair_count, " zone pairs");
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if zone is touched by price action                         |
//+------------------------------------------------------------------+
bool CZoneManager::IsZoneTouched(const ZoneInfo &zone, double high, double low, double close)
{
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default value
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      atr = atr_values[0];
   }
   IndicatorRelease(atr_handle);
   double tolerance = atr * m_touch_tolerance;
   
   // Expand zone boundaries with tolerance
   double upper_bound = zone.upper_price + tolerance;
   double lower_bound = zone.lower_price - tolerance;
   
   // Check if any price level touched the zone
   bool touched = (high >= lower_bound && low <= upper_bound) ||
                  (close >= lower_bound && close <= upper_bound);
   
   return touched;
}

//+------------------------------------------------------------------+
//| Update freshness status of all zones                             |
//+------------------------------------------------------------------+
bool CZoneManager::UpdateFreshness(datetime current_time)
{
   bool updated = false;
   
   for(int i = 0; i < m_zone_count; i++)
   {
      if(m_zones[i].status != ZONE_FRESH)
         continue;
      
      // Check bars since zone creation
      datetime start_time = m_zones[i].created_at;
      int bars_to_check = Bars(m_symbol, PERIOD_H1, start_time, current_time);
      
      if(bars_to_check <= 0)
         continue;
      
      // Check each bar for touches
      for(int bar = 1; bar < bars_to_check; bar++) // Start from bar 1 (skip creation bar)
      {
         double high = iHigh(m_symbol, PERIOD_H1, bar);
         double low = iLow(m_symbol, PERIOD_H1, bar);
         double close = iClose(m_symbol, PERIOD_H1, bar);
         
         if(IsZoneTouched(m_zones[i], high, low, close))
         {
            m_zones[i].status = ZONE_USED;
            m_zones[i].touched_at = iTime(m_symbol, PERIOD_H1, bar);
            updated = true;
            
            Print("Zone ", i, " (", EnumToString(m_zones[i].type), 
                  ") touched at ", TimeToString(m_zones[i].touched_at));
            break;
         }
      }
      
      // Check for stale zones (optional TTL)
      int hours_since_creation = (int)((current_time - m_zones[i].created_at) / 3600);
      if(hours_since_creation > 168) // 1 week
      {
         m_zones[i].status = ZONE_STALE;
         updated = true;
      }
   }
   
   return updated;
}

//+------------------------------------------------------------------+
//| Merge nearby zones to avoid clustering                           |
//+------------------------------------------------------------------+
bool CZoneManager::MergeNearbyZones(double min_distance)
{
   if(m_zone_count <= 1)
      return false;
   
   bool merged = false;
   
   for(int i = 0; i < m_zone_count - 1; i++)
   {
      for(int j = i + 1; j < m_zone_count; j++)
      {
         double distance = MathAbs(m_zones[i].mid_price - m_zones[j].mid_price);
         
         if(distance < min_distance)
         {
            // Merge zones - keep the one with higher volume
            if(m_zones[j].volume > m_zones[i].volume)
            {
               // Remove zone i
               for(int k = i; k < m_zone_count - 1; k++)
                  m_zones[k] = m_zones[k + 1];
            }
            else
            {
               // Remove zone j
               for(int k = j; k < m_zone_count - 1; k++)
                  m_zones[k] = m_zones[k + 1];
            }
            
            m_zone_count--;
            merged = true;
            break;
         }
      }
      
      if(merged)
         break;
   }
   
   return merged;
}

//+------------------------------------------------------------------+
//| Get fresh zones only                                             |
//+------------------------------------------------------------------+
bool CZoneManager::GetFreshZones(ZoneInfo &fresh_zones[], int &count) const
{
   count = 0;
   
   // Count fresh zones first
   for(int i = 0; i < m_zone_count; i++)
   {
      if(m_zones[i].status == ZONE_FRESH)
         count++;
   }
   
   if(count == 0)
      return false;
   
   ArrayResize(fresh_zones, count);
   int index = 0;
   
   for(int i = 0; i < m_zone_count; i++)
   {
      if(m_zones[i].status == ZONE_FRESH)
      {
         fresh_zones[index] = m_zones[i];
         index++;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Create zone pairs from individual zones                          |
//+------------------------------------------------------------------+
bool CZoneManager::CreateZonePairs()
{
   m_pair_count = 0;
   ArrayResize(m_zone_pairs, 0);
   
   if(m_zone_count < 2)
      return false;
   
   // Create pairs from HVN and LVN zones
   for(int i = 0; i < m_zone_count; i++)
   {
      for(int j = i + 1; j < m_zone_count; j++)
      {
         if(IsValidZonePair(m_zones[i], m_zones[j]))
         {
            ZonePair pair;
            
            // Determine which is support and which is resistance
            if(m_zones[i].mid_price < m_zones[j].mid_price)
            {
               pair.support_zone = m_zones[i];
               pair.resistance_zone = m_zones[j];
            }
            else
            {
               pair.support_zone = m_zones[j];
               pair.resistance_zone = m_zones[i];
            }
            
            CalculatePairMetrics(pair);
            pair.is_valid_pair = true;
            pair.created_at = TimeCurrent();
            
            ArrayResize(m_zone_pairs, m_pair_count + 1);
            m_zone_pairs[m_pair_count++] = pair;
         }
      }
   }
   
   Print("Created ", m_pair_count, " zone pairs");
   return m_pair_count > 0;
}

//+------------------------------------------------------------------+
//| Check if two zones can form a valid pair                        |
//+------------------------------------------------------------------+
bool CZoneManager::IsValidZonePair(const ZoneInfo &zone1, const ZoneInfo &zone2)
{
   // 1. Must be different types (HVN + LVN or POC + HVN/LVN)
   if(zone1.type == zone2.type)
      return false;
   
   // 2. Must have appropriate distance (not too close, not too far)
   double distance = MathAbs(zone1.mid_price - zone2.mid_price);
   double atr = GetCurrentATR();
   
   if(distance < atr * 0.5)  // Too close
      return false;
   
   if(distance > atr * 5.0)  // Too far
      return false;
   
   // 3. Must have sufficient volume
   if(zone1.volume < 100 || zone2.volume < 100)
      return false;
   
   // 4. Both zones should be fresh
   if(zone1.status != ZONE_FRESH || zone2.status != ZONE_FRESH)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Calculate metrics for a zone pair                                |
//+------------------------------------------------------------------+
void CZoneManager::CalculatePairMetrics(ZonePair &pair)
{
   double atr = GetCurrentATR();
   
   // Calculate range height
   pair.range_height = pair.resistance_zone.mid_price - pair.support_zone.mid_price;
   
   // Calculate range ratio (range height / ATR)
   pair.range_ratio = (atr > 0) ? pair.range_height / atr : 0;
   
   // Calculate combined pair score
   pair.pair_score = (pair.support_zone.score + pair.resistance_zone.score) / 2.0;
   
   // Bonus for good range ratio (1.0 - 3.0 ATR is ideal)
   if(pair.range_ratio >= 1.0 && pair.range_ratio <= 3.0)
      pair.pair_score += 1.0;
}

//+------------------------------------------------------------------+
//| Get current ATR value                                            |
//+------------------------------------------------------------------+
double CZoneManager::GetCurrentATR()
{
   int atr_handle = iATR(m_symbol, PERIOD_H1, 14);
   double atr_values[];
   ArrayResize(atr_values, 1);
   double atr = 0.001; // Default value
   
   if(CopyBuffer(atr_handle, 0, 1, 1, atr_values) > 0)
   {
      atr = atr_values[0];
   }
   IndicatorRelease(atr_handle);
   
   return atr;
}

//+------------------------------------------------------------------+
//| Filter valid zone pairs                                          |
//+------------------------------------------------------------------+
bool CZoneManager::FilterValidZonePairs()
{
   if(m_pair_count <= 0)
      return false;
   
   // Sort pairs by score
   SortPairsByScore();
   
   // Keep only the best pairs
   if(m_pair_count > m_max_pairs)
   {
      m_pair_count = m_max_pairs;
      ArrayResize(m_zone_pairs, m_pair_count);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Sort zone pairs by score                                         |
//+------------------------------------------------------------------+
void CZoneManager::SortPairsByScore()
{
   for(int i = 0; i < m_pair_count - 1; i++)
   {
      for(int j = i + 1; j < m_pair_count; j++)
      {
         if(m_zone_pairs[i].pair_score < m_zone_pairs[j].pair_score)
         {
            ZonePair temp = m_zone_pairs[i];
            m_zone_pairs[i] = m_zone_pairs[j];
            m_zone_pairs[j] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get zone by index                                                |
//+------------------------------------------------------------------+
bool CZoneManager::GetZone(int index, ZoneInfo &zone) const
{
   if(index < 0 || index >= m_zone_count)
      return false;
   
   zone = m_zones[index];
   return true;
}

//+------------------------------------------------------------------+
//| Clear all zones                                                  |
//+------------------------------------------------------------------+
void CZoneManager::ClearZones()
{
   ArrayResize(m_zones, 0);
   m_zone_count = 0;
}

//+------------------------------------------------------------------+
//| Print zones information                                          |
//+------------------------------------------------------------------+
void CZoneManager::PrintZones() const
{
   Print("=== Zone Manager Status ===");
   Print("Total zones: ", m_zone_count);
   
   for(int i = 0; i < m_zone_count; i++)
   {
      Print("Zone ", i, ": ", EnumToString(m_zones[i].type), 
            " | Price: ", DoubleToString(m_zones[i].mid_price, 5),
            " | Status: ", EnumToString(m_zones[i].status),
            " | Score: ", DoubleToString(m_zones[i].score, 2));
   }
}

//+------------------------------------------------------------------+
//| Get zone pair by index                                           |
//+------------------------------------------------------------------+
bool CZoneManager::GetZonePair(int index, ZonePair &pair) const
{
   if(index < 0 || index >= m_pair_count)
      return false;
   
   pair = m_zone_pairs[index];
   return true;
}

//+------------------------------------------------------------------+
//| Get fresh zone pairs                                             |
//+------------------------------------------------------------------+
bool CZoneManager::GetFreshZonePairs(ZonePair &fresh_pairs[], int &count) const
{
   count = 0;
   
   for(int i = 0; i < m_pair_count; i++)
   {
      if(m_zone_pairs[i].is_valid_pair)
      {
         ArrayResize(fresh_pairs, count + 1);
         fresh_pairs[count++] = m_zone_pairs[i];
      }
   }
   
   return count > 0;
}
