//+------------------------------------------------------------------+
//|                                                 CounterAttack.mq5 |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"
#property description "Volume Profile based Counter Attack Trading Bot"

// Include all necessary classes
#include "includes/Constants.mqh"
#include "classes/TickDataLoader.mqh"
#include "classes/VolumeProfileCalculator.mqh"
#include "classes/ZoneManager.mqh"
#include "classes/FilterSystem.mqh"
#include "classes/ScoringSystem.mqh"
#include "classes/TradingEngine.mqh"
#include "classes/OutputSystem.mqh"
#include "classes/MultiTimeframeAnalyzer.mqh"
#include "classes/DynamicPositionSizer.mqh"
#include "classes/PerformanceMonitor.mqh"

//--- Input Parameters
//=== Range Settings ===
input ENUM_RANGE_MODE InpRangeMode = RANGE_SESSION;        // Range calculation mode
input int             InpSessionStartHour = 0;             // Session start hour
input int             InpSessionStartMinute = 0;           // Session start minute
input int             InpSessionEndHour = 23;              // Session end hour
input int             InpSessionEndMinute = 59;            // Session end minute
input int             InpBarsBack = 24;                    // Bars back for FIXED_RANGE mode

//=== Volume Profile Settings ===
input double          InpBinSizePoints = 15.0;             // Bin size in points
input int             InpSmoothWindow = 3;                 // Smoothing window
input double          InpHVNPercentile = 80.0;             // HVN percentile threshold
input double          InpLVNPercentile = 25.0;             // LVN percentile threshold

//=== Zone Settings ===
input double          InpZoneWidthMultiplier = 0.25;       // Zone width (ATR multiplier)
input double          InpTouchTolerance = 0.25;            // Touch tolerance (ATR multiplier)
input int             InpMaxZones = 10;                    // Maximum zones to keep
input int             InpMaxZonePairs = 5;                 // Maximum zone pairs to keep
input int             InpMinTicks = 1500;                  // Minimum ticks required
input bool            InpUseZonePairing = true;            // Enable zone pairing (support + resistance)

//=== Zone Management Settings ===
input bool            InpAutoRemoveTestedZones = true;     // Auto remove tested zones
input int             InpMinSupportZones = 2;              // Minimum support zones to maintain
input int             InpMinResistanceZones = 2;           // Minimum resistance zones to maintain
input bool            InpRecalculateOnZoneTest = true;     // Recalculate when zone is tested

//=== Filter Settings ===
input bool            InpUseVolatilityFilter = true;       // Enable volatility filter
input bool            InpUseTrendFilter = true;            // Enable trend filter
input bool            InpUsePriceActionFilter = true;      // Enable price action filter
input double          InpMinATRRatio = 1.0;                // Minimum ATR ratio
input int             InpTrendMAPeriod = 50;               // Trend MA period
input double          InpRejectionWickRatio = 0.6;         // Rejection wick ratio
input double          InpVolumeSpikeRatio = 1.2;           // Volume spike ratio

//=== Trading Settings ===
input bool            InpEnableTrading = true;             // Enable live trading
input double          InpLotSize = 0.01;                   // Fixed lot size (0 = auto)
input double          InpRiskPercent = 2.0;                // Risk per trade (%)
input double          InpRiskRewardRatio = 2.0;            // Risk:Reward ratio
input int             InpMaxPositions = 3;                 // Maximum open positions
input int             InpMagicNumber = 12345;              // Magic number

//=== Output Settings ===
input bool            InpDrawZones = true;                 // Draw zones on chart
input bool            InpSaveToFile = true;                // Save zones to file
input bool            InpEnableAlerts = true;              // Enable alerts
input bool            InpEnablePushNotifications = false;  // Enable push notifications
input int             InpUpdateIntervalSeconds = 30;       // Update interval in seconds

//--- Global Variables
CTickDataLoader*      g_tickLoader = NULL;
CVolumeProfileCalculator* g_profileCalc = NULL;
CZoneManager*         g_zoneManager = NULL;
CFilterSystem*        g_filterSystem = NULL;
CScoringSystem*       g_scoringSystem = NULL;
COutputSystem*        g_outputSystem = NULL;
CTradingEngine*       g_tradingEngine = NULL;
CMultiTimeframeAnalyzer* g_mtfAnalyzer = NULL;
CDynamicPositionSizer* g_positionSizer = NULL;
CPerformanceMonitor*  g_performanceMonitor = NULL;

datetime              g_lastCalculationTime = 0;
datetime              g_lastUpdateTime = 0;
bool                  g_isInitialized = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== CounterAttack Bot Initialization ===");
   
   // Initialize all components
   string symbol = ChartSymbol();
   
   g_tickLoader = new CTickDataLoader(symbol);
   g_profileCalc = new CVolumeProfileCalculator();
   g_zoneManager = new CZoneManager(symbol);
   g_filterSystem = new CFilterSystem(symbol);
   g_scoringSystem = new CScoringSystem(symbol, InpMaxZones);
   g_outputSystem = new COutputSystem(symbol);
   g_tradingEngine = new CTradingEngine(symbol, PERIOD_H1);
   g_mtfAnalyzer = new CMultiTimeframeAnalyzer(symbol);
   g_positionSizer = new CDynamicPositionSizer(symbol);
   g_performanceMonitor = new CPerformanceMonitor(symbol, InpMagicNumber);

   if(!g_tickLoader || !g_profileCalc || !g_zoneManager ||
      !g_filterSystem || !g_scoringSystem || !g_outputSystem ||
      !g_tradingEngine || !g_mtfAnalyzer || !g_positionSizer || !g_performanceMonitor)
   {
      Print("Error: Failed to initialize components");
      return INIT_FAILED;
   }
   
   // Configure components
   ConfigureComponents();
   
   // Set timer for updates
   EventSetTimer(InpUpdateIntervalSeconds);
   
   // Perform initial calculation
   if(!PerformInitialCalculation())
   {
      Print("Warning: Initial calculation failed, will retry on timer");
   }
   
   g_isInitialized = true;
   Print("CounterAttack Bot initialized successfully");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("CounterAttack Bot shutting down...");
   
   EventKillTimer();
   
   // Clean up objects
   if(g_outputSystem)
   {
      g_outputSystem.ClearChartObjects();
      delete g_outputSystem;
   }
   
   if(g_performanceMonitor) delete g_performanceMonitor;
   if(g_positionSizer) delete g_positionSizer;
   if(g_mtfAnalyzer) delete g_mtfAnalyzer;
   if(g_tradingEngine) delete g_tradingEngine;
   if(g_scoringSystem) delete g_scoringSystem;
   if(g_filterSystem) delete g_filterSystem;
   if(g_zoneManager) delete g_zoneManager;
   if(g_profileCalc) delete g_profileCalc;
   if(g_tickLoader) delete g_tickLoader;
   
   Print("CounterAttack Bot shutdown complete");
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(!g_isInitialized)
      return;
   
   datetime current_time = TimeCurrent();
   
   // Update zone freshness และตรวจสอบว่า zone ์เทส
   bool zones_tested = false;
   if(g_zoneManager && g_zoneManager.UpdateFreshness(current_time))
   {
      zones_tested = true;
      Print("Zones were tested - will recalculate profile");
   }
   
   // Check if we need to recalculate profile (new session/range zone ์เทส)
   if(ShouldRecalculateProfile(current_time) || zones_tested)
   {
      Print("Recalculating volume profile...");
      if(CalculateVolumeProfile())
      {
         g_lastCalculationTime = current_time;
      }
   }
   
   // Update performance monitoring
   if(g_performanceMonitor)
   {
      g_performanceMonitor.UpdatePerformance();
   }

   // Update trading engine positions
   if(g_tradingEngine)
   {
      g_tradingEngine.UpdatePositions();
      g_tradingEngine.CheckExitSignals();
   }

   // Process and output zones
   ProcessAndOutputZones();

   // Process trading signals if zones were updated
   if(zones_tested || ShouldRecalculateProfile(current_time))
   {
      ProcessTradingSignals();
   }

   g_lastUpdateTime = current_time;
}

//+------------------------------------------------------------------+
//| Process trading signals from zones                               |
//+------------------------------------------------------------------+
void ProcessTradingSignals()
{
   if(!g_tradingEngine || !g_zoneManager || !g_mtfAnalyzer)
      return;

   // Update multi-timeframe analysis
   if(!g_mtfAnalyzer.UpdateAnalysis())
   {
      Print("Failed to update multi-timeframe analysis");
      return;
   }

   // Get current price
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // Get all zones from zone manager
   ZoneInfo zones[];
   int zone_count = g_zoneManager.GetAllZones(zones);

   if(zone_count <= 0)
   {
      Print("No zones available for trading signals");
      return;
   }

   // Process each zone for potential trading signals
   for(int i = 0; i < zone_count; i++)
   {
      ZoneInfo zone = zones[i];

      // Skip broken zones
      if(zone.status == ZONE_BROKEN)
         continue;

      // Check if price is near the zone
      double zone_distance = MathAbs(current_price - zone.mid_price);
      double zone_height = zone.upper_price - zone.lower_price;

      if(zone_distance > zone_height * 2.0) // Price too far from zone
         continue;

      // Determine if this is a support or resistance zone based on current price position
      bool is_support_zone = (current_price >= zone.lower_price && current_price <= zone.upper_price * 1.1);
      bool is_resistance_zone = (current_price <= zone.upper_price && current_price >= zone.lower_price * 0.9);

      // Process buy signals (support zones)
      if(is_support_zone)
      {
         // Validate with multi-timeframe analysis
         if(g_mtfAnalyzer.ValidateBuySignal(zone))
         {
            // Get signal strength
            double signal_strength = g_mtfAnalyzer.GetSignalStrength(zone, ORDER_TYPE_BUY);

            // Process the signal
            if(g_tradingEngine.ProcessZoneSignal(zone, current_price))
            {
               // Send alert
               string message = StringFormat("BUY signal at %.5f (Strength: %.2f)",
                                           zone.mid_price, signal_strength);
               g_outputSystem.SendTradeAlert("BUY", current_price, message);

               // Draw signal on chart
               g_outputSystem.DrawEntrySignal(current_price, ORDER_TYPE_BUY, message);

               Print("BUY signal processed: ", message);
            }
         }
      }

      // Process sell signals (resistance zones)
      if(is_resistance_zone)
      {
         // Validate with multi-timeframe analysis
         if(g_mtfAnalyzer.ValidateSellSignal(zone))
         {
            // Get signal strength
            double signal_strength = g_mtfAnalyzer.GetSignalStrength(zone, ORDER_TYPE_SELL);

            // Process the signal
            if(g_tradingEngine.ProcessZoneSignal(zone, current_price))
            {
               // Send alert
               string message = StringFormat("SELL signal at %.5f (Strength: %.2f)",
                                           zone.mid_price, signal_strength);
               g_outputSystem.SendTradeAlert("SELL", current_price, message);

               // Draw signal on chart
               g_outputSystem.DrawEntrySignal(current_price, ORDER_TYPE_SELL, message);

               Print("SELL signal processed: ", message);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Configure all components with input parameters                   |
//+------------------------------------------------------------------+
void ConfigureComponents()
{
   // Configure Volume Profile Calculator
   g_profileCalc.SetSmoothWindow(InpSmoothWindow);
   g_profileCalc.SetHVNPercentile(InpHVNPercentile);
   g_profileCalc.SetLVNPercentile(InpLVNPercentile);
   
   // Configure Zone Manager
   g_zoneManager.SetZoneWidthMultiplier(InpZoneWidthMultiplier);
   g_zoneManager.SetTouchTolerance(InpTouchTolerance);
   g_zoneManager.SetMaxZones(InpMaxZones);
   g_zoneManager.SetMaxZonePairs(InpMaxZonePairs);
   g_zoneManager.SetUseZonePairing(InpUseZonePairing);
   
   // Configure Filter System
   FilterParams filter_params;
   filter_params.use_volatility_filter = InpUseVolatilityFilter;
   filter_params.use_trend_filter = InpUseTrendFilter;
   filter_params.use_price_action_filter = InpUsePriceActionFilter;
   filter_params.min_atr_ratio = InpMinATRRatio;
   filter_params.atr_lookback_periods = 50;
   filter_params.trend_ma_period = InpTrendMAPeriod;
   filter_params.trend_ma_fast_period = InpTrendMAPeriod / 2;
   filter_params.trend_strength_threshold = 0.1;
   filter_params.rejection_wick_ratio = InpRejectionWickRatio;
   filter_params.volume_spike_ratio = InpVolumeSpikeRatio;
   filter_params.volume_lookback_periods = 20;

   g_filterSystem.SetFilterParams(filter_params);

   // Configure trading engine
   if(g_tradingEngine)
   {
      g_tradingEngine.SetLotSize(InpLotSize);
      g_tradingEngine.SetRiskPercent(InpRiskPercent);
      g_tradingEngine.SetRiskRewardRatio(InpRiskRewardRatio);
      g_tradingEngine.SetMaxPositions(InpMaxPositions);
      g_tradingEngine.SetMagicNumber(InpMagicNumber);
      g_tradingEngine.SetDailyLimits(1000.0, 3000.0); // $1000 max loss, $3000 max profit

      if(!InpEnableTrading)
         g_tradingEngine.DisableTrading();
   }

   // Configure position sizer
   if(g_positionSizer)
   {
      PositionSizingParams sizing_params;
      sizing_params.base_risk_percent = InpRiskPercent;
      sizing_params.max_risk_percent = InpRiskPercent * 2.0;
      sizing_params.min_risk_percent = InpRiskPercent * 0.5;
      sizing_params.use_volatility_scaling = true;
      sizing_params.use_confidence_scaling = true;
      sizing_params.use_equity_curve_scaling = true;
      sizing_params.max_position_size = 1.0;
      sizing_params.min_position_size = 0.01;

      g_positionSizer.SetParams(sizing_params);
   }

   // Configure Output System
   g_outputSystem.SetChartDrawing(InpDrawZones);
   g_outputSystem.SetFileOutput(InpSaveToFile);
   g_outputSystem.SetAlerts(InpEnableAlerts);
   g_outputSystem.SetPushNotifications(InpEnablePushNotifications);
}

//+------------------------------------------------------------------+
//| Check if we should recalculate the volume profile               |
//+------------------------------------------------------------------+
bool ShouldRecalculateProfile(datetime current_time)
{
   if(g_lastCalculationTime == 0)
      return true; // First calculation
   
   if(InpRangeMode == RANGE_SESSION)
   {
      // Check if we've moved to a new session
      SessionTime session;
      session.start_hour = InpSessionStartHour;
      session.start_minute = InpSessionStartMinute;
      session.end_hour = InpSessionEndHour;
      session.end_minute = InpSessionEndMinute;
      
      datetime last_session_end = GetLastSessionEnd(g_lastCalculationTime, session);
      datetime current_session_end = GetLastSessionEnd(current_time, session);
      
      return (current_session_end > last_session_end);
   }
   else // RANGE_FIXED
   {
      // Recalculate every hour for fixed range
      return (current_time - g_lastCalculationTime) >= 3600;
   }
}

//+------------------------------------------------------------------+
//| Get the end time of the last completed session                  |
//+------------------------------------------------------------------+
datetime GetLastSessionEnd(datetime reference_time, const SessionTime &session)
{
   MqlDateTime dt;
   TimeToStruct(reference_time, dt);
   
   // Calculate session end for the reference date
   datetime session_end = StructToTime(dt) - (dt.hour * 3600 + dt.min * 60 + dt.sec) +
                          session.end_hour * 3600 + session.end_minute * 60;
   
   // If current time is before session end, use previous day's session
   if(reference_time < session_end)
      session_end -= 86400; // Subtract one day
   
   return session_end;
}

//+------------------------------------------------------------------+
//| Perform initial calculation                                      |
//+------------------------------------------------------------------+
bool PerformInitialCalculation()
{
   return CalculateVolumeProfile();
}

//+------------------------------------------------------------------+
//| Main volume profile calculation                                  |
//+------------------------------------------------------------------+
bool CalculateVolumeProfile()
{
   if(!g_tickLoader || !g_profileCalc || !g_zoneManager)
      return false;
   
   // Determine time range for calculation
   datetime from_time, to_time;
   if(!GetCalculationTimeRange(from_time, to_time))
   {
      Print("Error: Could not determine calculation time range");
      return false;
   }
   
   Print("Calculating volume profile from ", TimeToString(from_time), 
         " to ", TimeToString(to_time));
   
   // Load tick data
   if(!g_tickLoader.LoadTicksRange(from_time, to_time))
   {
      Print("Error: Failed to load tick data");
      return false;
   }
   
   if(g_tickLoader.GetTickCount() < InpMinTicks)
   {
      Print("Warning: Insufficient tick data (", g_tickLoader.GetTickCount(), 
            " < ", InpMinTicks, ")");
      return false;
   }
   
   // Get tick data
   TickData ticks[];
   if(!g_tickLoader.GetTicksArray(ticks))
   {
      Print("Error: Failed to get tick array");
      return false;
   }
   
   // Calculate volume profile
   if(!g_profileCalc.CalculateProfile(ticks, ArraySize(ticks), 
                                     InpBinSizePoints, ChartSymbol()))
   {
      Print("Error: Failed to calculate volume profile");
      return false;
   }
   
   // Find nodes
   int poc_index;
   double poc_volume;
   if(!g_profileCalc.FindPOC(poc_index, poc_volume))
   {
      Print("Error: Failed to find POC");
      return false;
   }
   
   int hvn_indices[100];
   int hvn_count;
   if(!g_profileCalc.FindHVNNodes(hvn_indices, hvn_count))
   {
      Print("Warning: No HVN nodes found");
      hvn_count = 0;
   }
   
   int lvn_indices[100];
   int lvn_count;
   if(!g_profileCalc.FindLVNNodes(lvn_indices, lvn_count))
   {
      Print("Warning: No LVN nodes found");
      lvn_count = 0;
   }
   
   // Get bins for zone creation
   VolumeProfileBin bins[];
   int bin_count = g_profileCalc.GetBinCount();
   ArrayResize(bins, bin_count);
   
   for(int i = 0; i < bin_count; i++)
   {
      g_profileCalc.GetBin(i, bins[i]);
   }
   
   // Create zones
   if(!g_zoneManager.CreateZonesFromNodes(poc_index, hvn_indices, hvn_count,
                                         lvn_indices, lvn_count, bins, bin_count, to_time))
   {
      Print("Error: Failed to create zones");
      return false;
   }
   
   // Process and output zones
   return ProcessAndOutputZones();
}

//+------------------------------------------------------------------+
//| Get calculation time range based on mode                        |
//+------------------------------------------------------------------+
bool GetCalculationTimeRange(datetime &from_time, datetime &to_time)
{
   datetime current_time = TimeCurrent();
   
   if(InpRangeMode == RANGE_SESSION)
   {
      SessionTime session;
      session.start_hour = InpSessionStartHour;
      session.start_minute = InpSessionStartMinute;
      session.end_hour = InpSessionEndHour;
      session.end_minute = InpSessionEndMinute;
      
      // Use previous completed session
      to_time = GetLastSessionEnd(current_time, session);
      from_time = to_time - 86400; // Previous day
      
      // Adjust for session times
      MqlDateTime dt;
      TimeToStruct(from_time, dt);
      from_time = StructToTime(dt) - (dt.hour * 3600 + dt.min * 60 + dt.sec) +
                  session.start_hour * 3600 + session.start_minute * 60;
   }
   else // RANGE_FIXED
   {
      to_time = iTime(ChartSymbol(), PERIOD_H1, 1); // Previous completed bar
      from_time = iTime(ChartSymbol(), PERIOD_H1, InpBarsBack);
   }
   
   return (from_time > 0 && to_time > from_time);
}

//+------------------------------------------------------------------+
//| Enhanced Business Process Flow:                                  |
//| Tick → Bin → HVN/LVN → Zone(ATR) → Freshness → Advanced Filters → Score → Output |
//+------------------------------------------------------------------+
bool ProcessAndOutputZones()
{
   if(!g_zoneManager || !g_filterSystem || !g_scoringSystem || !g_outputSystem)
      return false;

   Print("=== Enhanced Zone Processing Pipeline ===");

   // Step 1: Get fresh zones
   ZoneInfo fresh_zones[];
   int fresh_count;
   if(!g_zoneManager.GetFreshZones(fresh_zones, fresh_count))
   {
      Print("No fresh zones available - need to recalculate");
      return false;
   }

   Print("Step 1: Found ", fresh_count, " fresh zones");

   // Step 2: Apply Advanced Filters
   ZoneInfo filtered_zones[];
   int filtered_count;
   if(!g_filterSystem.FilterZones(fresh_zones, fresh_count, filtered_zones, filtered_count))
   {
      Print("Step 2: No zones passed advanced filters");
      return false;
   }

   Print("Step 2: ", filtered_count, " zones passed advanced filters");

   // Step 3: Score zones
   if(!g_scoringSystem.ScoreZones(filtered_zones, filtered_count))
   {
      Print("Error: Failed to score zones");
      return false;
   }

   // Step 4: Rank and limit zones
   if(!g_scoringSystem.RankAndLimitZones(filtered_zones, filtered_count))
   {
      Print("Error: Failed to rank zones");
      return false;
   }

   // Step 5: แยก Support/Resistance zones
   CheckSupportResistanceZones();

   // Step 6: Output zones
   bool success = g_outputSystem.OutputZones(filtered_zones, filtered_count);

   if(success)
   {
      Print("=== Zone Processing Complete ===");
      Print("Final output: ", filtered_count, " high-quality zones");
   }

   return success;
}

//+------------------------------------------------------------------+
//| Expert tick function (optional - for real-time updates)         |
//+------------------------------------------------------------------+
void OnTick()
{
   // Optional: Add real-time price monitoring here
   // This function is called on every tick
   // Can be used for immediate zone touch detection
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Handle chart events if needed
   // Can be used for manual zone management or user interaction
}

// เ่่ม ค์ ตรวจสอบ Support/Resistance zones
void CheckSupportResistanceZones()
{
   if(!g_zoneManager) return;
   
   ZoneInfo support_zones[];
   ZoneInfo resistance_zones[];
   int support_count, resistance_count;
   
   if(g_zoneManager.GetSupportResistanceZones(support_zones, resistance_zones, 
                                             support_count, resistance_count))
   {
      Print("=== Support/Resistance Analysis ===");
      Print("Support zones: ", support_count);
      Print("Resistance zones: ", resistance_count);
      
      // แสดงข้อมูล support zones
      for(int i = 0; i < support_count; i++)
      {
         Print("Support ", i+1, ": ", DoubleToString(support_zones[i].mid_price, _Digits), 
               " Score: ", DoubleToString(support_zones[i].score, 2));
      }
      
      // แสดงข้อมูล resistance zones
      for(int i = 0; i < resistance_count; i++)
      {
         Print("Resistance ", i+1, ": ", DoubleToString(resistance_zones[i].mid_price, _Digits),
               " Score: ", DoubleToString(resistance_zones[i].score, 2));
      }
   }
}
