//+------------------------------------------------------------------+
//|                                                 OutputSystem.mqh |
//|                                  CounterAttack Trading Bot v1.0  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"

#include "../includes/Constants.mqh"

//+------------------------------------------------------------------+
//| Output System Class                                              |
//+------------------------------------------------------------------+
class COutputSystem
{
private:
   string            m_symbol;
   bool              m_chart_drawing_enabled;
   bool              m_file_output_enabled;
   bool              m_alerts_enabled;
   bool              m_push_notifications_enabled;
   
   string            m_output_folder;
   int               m_file_handle;
   
   // Chart objects tracking
   string            m_zone_objects[];
   string            m_signal_objects[];
   
   // Alert settings
   datetime          m_last_alert_time;
   int               m_alert_cooldown; // seconds
   
   // Helper methods
   string            GenerateZoneObjectName(const ZoneInfo &zone);
   string            GenerateSignalObjectName(int signal_id);
   color             GetZoneColor(const ZoneInfo &zone);
   void              CleanupOldObjects();
   bool              WriteToFile(string message);
   
public:
   // Constructor/Destructor
                     COutputSystem(string symbol);
                    ~COutputSystem();
   
   // Configuration
   void              SetChartDrawing(bool enabled) { m_chart_drawing_enabled = enabled; }
   void              SetFileOutput(bool enabled) { m_file_output_enabled = enabled; }
   void              SetAlerts(bool enabled) { m_alerts_enabled = enabled; }
   void              SetPushNotifications(bool enabled) { m_push_notifications_enabled = enabled; }
   void              SetAlertCooldown(int seconds) { m_alert_cooldown = seconds; }
   
   // Zone visualization
   bool              DrawZone(const ZoneInfo &zone);
   bool              UpdateZone(const ZoneInfo &zone);
   bool              RemoveZone(const ZoneInfo &zone);
   void              ClearAllZones();
   
   // Signal visualization
   bool              DrawEntrySignal(double price, ENUM_ORDER_TYPE signal_type, string reason);
   bool              DrawExitSignal(double price, string reason);
   void              ClearSignals();
   
   // Alerts and notifications
   bool              SendZoneAlert(const ZoneInfo &zone, string message);
   bool              SendTradeAlert(string trade_action, double price, string reason);
   bool              SendDailyReport();
   
   // File output
   bool              LogZone(const ZoneInfo &zone);
   bool              LogTrade(string action, double price, double lot_size, string reason);
   bool              LogPerformance(double daily_pl, double win_rate, double profit_factor);
   
   // Chart management
   void              UpdateChart();
   void              CleanupChart();
   
   // Status display
   void              ShowStatus(string status_text);
   void              ShowStatistics(int positions, double daily_pl, double floating_pl);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
COutputSystem::COutputSystem(string symbol)
{
   m_symbol = symbol;
   m_chart_drawing_enabled = true;
   m_file_output_enabled = true;
   m_alerts_enabled = true;
   m_push_notifications_enabled = false;
   
   m_output_folder = "CounterAttack\\";
   m_file_handle = INVALID_HANDLE;
   m_last_alert_time = 0;
   m_alert_cooldown = 300; // 5 minutes
   
   // Create output directory
   if(m_file_output_enabled)
   {
      FolderCreate(m_output_folder, FILE_COMMON);
   }
   
   Print("Output System initialized for ", m_symbol);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
COutputSystem::~COutputSystem()
{
   CleanupChart();
   
   if(m_file_handle != INVALID_HANDLE)
   {
      FileClose(m_file_handle);
   }
   
   Print("Output System destroyed");
}

//+------------------------------------------------------------------+
//| Draw zone on chart                                               |
//+------------------------------------------------------------------+
bool COutputSystem::DrawZone(const ZoneInfo &zone)
{
   if(!m_chart_drawing_enabled)
      return true;
   
   string obj_name = GenerateZoneObjectName(zone);
   color zone_color = GetZoneColor(zone);
   
   // Create rectangle object
   if(!ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0, 
                    zone.created_at, zone.lower_price,
                    zone.created_at + 86400, zone.upper_price))
   {
      Print("Failed to create zone object: ", obj_name);
      return false;
   }
   
   // Set object properties
   ObjectSetInteger(0, obj_name, OBJPROP_COLOR, zone_color);
   ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
   ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
   ObjectSetInteger(0, obj_name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, obj_name, OBJPROP_HIDDEN, true);
   
   // Set transparency
   ObjectSetInteger(0, obj_name, OBJPROP_BGCOLOR, zone_color);
   
   // Add description
   string description = StringFormat("Zone: %s | Price: %.5f | Volume: %.0f | Status: %s",
                                   EnumToString((ENUM_ZONE_TYPE)zone.type),
                                   zone.mid_price,
                                   zone.volume,
                                   EnumToString((ENUM_ZONE_STATUS)zone.status));
   
   ObjectSetString(0, obj_name, OBJPROP_TOOLTIP, description);
   
   // Add to tracking array
   int size = ArraySize(m_zone_objects);
   ArrayResize(m_zone_objects, size + 1);
   m_zone_objects[size] = obj_name;
   
   return true;
}

//+------------------------------------------------------------------+
//| Generate zone object name                                        |
//+------------------------------------------------------------------+
string COutputSystem::GenerateZoneObjectName(const ZoneInfo &zone)
{
   return StringFormat("CounterAttack_Zone_%s_%.5f_%d", 
                      m_symbol, zone.mid_price, (int)zone.created_at);
}

//+------------------------------------------------------------------+
//| Get zone color based on type and status                          |
//+------------------------------------------------------------------+
color COutputSystem::GetZoneColor(const ZoneInfo &zone)
{
   color zone_base_color;
   
   // Base color by type
   switch(zone.type)
   {
      case ZONE_HVN:
         zone_base_color = clrDodgerBlue;
         break;
      case ZONE_LVN:
         zone_base_color = clrOrange;
         break;
      case ZONE_POC:
         zone_base_color = clrYellow;
         break;
      default:
         zone_base_color = clrGray;
   }
   
   // Modify color by status
   switch(zone.status)
   {
      case ZONE_FRESH:
         return zone_base_color;
      case ZONE_TESTED:
         return clrDarkGray;
      case ZONE_BROKEN:
         return clrRed;
      default:
         return zone_base_color;
   }
}

//+------------------------------------------------------------------+
//| Draw entry signal arrow                                          |
//+------------------------------------------------------------------+
bool COutputSystem::DrawEntrySignal(double price, ENUM_ORDER_TYPE signal_type, string reason)
{
   if(!m_chart_drawing_enabled)
      return true;
   
   static int signal_counter = 0;
   signal_counter++;
   
   string obj_name = GenerateSignalObjectName(signal_counter);
   datetime current_time = TimeCurrent();
   
   // Create arrow object
   int arrow_code = (signal_type == ORDER_TYPE_BUY) ? 233 : 234; // Up/Down arrows
   color arrow_color = (signal_type == ORDER_TYPE_BUY) ? clrLime : clrRed;
   
   if(!ObjectCreate(0, obj_name, OBJ_ARROW, 0, current_time, price))
   {
      Print("Failed to create signal arrow: ", obj_name);
      return false;
   }
   
   // Set arrow properties
   ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, arrow_code);
   ObjectSetInteger(0, obj_name, OBJPROP_COLOR, arrow_color);
   ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 3);
   ObjectSetInteger(0, obj_name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   
   // Add description
   string signal_text = (signal_type == ORDER_TYPE_BUY) ? "BUY" : "SELL";
   string description = StringFormat("%s Signal | Price: %.5f | Reason: %s",
                                   signal_text, price, reason);
   
   ObjectSetString(0, obj_name, OBJPROP_TOOLTIP, description);
   
   // Add to tracking array
   int size = ArraySize(m_signal_objects);
   ArrayResize(m_signal_objects, size + 1);
   m_signal_objects[size] = obj_name;
   
   return true;
}

//+------------------------------------------------------------------+
//| Generate signal object name                                      |
//+------------------------------------------------------------------+
string COutputSystem::GenerateSignalObjectName(int signal_id)
{
   return StringFormat("CounterAttack_Signal_%s_%d_%d", 
                      m_symbol, signal_id, (int)TimeCurrent());
}

//+------------------------------------------------------------------+
//| Send zone alert                                                  |
//+------------------------------------------------------------------+
bool COutputSystem::SendZoneAlert(const ZoneInfo &zone, string message)
{
   if(!m_alerts_enabled)
      return true;
   
   // Check cooldown
   if(TimeCurrent() - m_last_alert_time < m_alert_cooldown)
      return true;
   
   string alert_message = StringFormat("Zone Alert: %s | Price: %.5f | %s",
                                     EnumToString((ENUM_ZONE_TYPE)zone.type),
                                     zone.mid_price, message);
   
   Alert(alert_message);
   m_last_alert_time = TimeCurrent();
   
   // Log to file
   WriteToFile(alert_message);
   
   return true;
}

//+------------------------------------------------------------------+
//| Send trade alert                                                 |
//+------------------------------------------------------------------+
bool COutputSystem::SendTradeAlert(string trade_action, double price, string reason)
{
   if(!m_alerts_enabled)
      return true;
   
   // Check cooldown
   if(TimeCurrent() - m_last_alert_time < m_alert_cooldown)
      return true;
   
   string alert_message = StringFormat("Trade Alert: %s at %.5f | %s",
                                     trade_action, price, reason);
   
   Alert(alert_message);
   m_last_alert_time = TimeCurrent();
   
   // Send push notification if enabled
   if(m_push_notifications_enabled)
   {
      SendNotification(alert_message);
   }
   
   // Log to file
   WriteToFile(alert_message);
   
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup chart objects                                            |
//+------------------------------------------------------------------+
void COutputSystem::CleanupChart()
{
   // Clean up zone objects
   for(int i = 0; i < ArraySize(m_zone_objects); i++)
   {
      if(m_zone_objects[i] != "")
      {
         ObjectDelete(0, m_zone_objects[i]);
      }
   }
   
   // Clean up signal objects
   for(int i = 0; i < ArraySize(m_signal_objects); i++)
   {
      if(m_signal_objects[i] != "")
      {
         ObjectDelete(0, m_signal_objects[i]);
      }
   }
   
   // Clear arrays
   ArrayResize(m_zone_objects, 0);
   ArrayResize(m_signal_objects, 0);
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Write message to file                                            |
//+------------------------------------------------------------------+
bool COutputSystem::WriteToFile(string message)
{
   if(!m_file_output_enabled)
      return true;
   
   if(m_file_handle == INVALID_HANDLE)
   {
      string filename = m_output_folder + "CounterAttack_Log_" + 
                       TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
      m_file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT | FILE_ANSI);
      
      if(m_file_handle == INVALID_HANDLE)
      {
         Print("Error: Cannot create log file: ", filename);
         return false;
      }
   }
   
   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string log_entry = timestamp + " | " + message + "\n";
   
   FileWriteString(m_file_handle, log_entry);
   FileFlush(m_file_handle);
   
   return true;
}
