//+------------------------------------------------------------------+
//| StrategyTester.mq5 - ทดสอบกลยุทธ์การเทรด
//+------------------------------------------------------------------+
#property copyright "MyFirstBot"
#property link      ""
#property version   "1.00"
#property description "ทดสอบกลยุทธ์การเทรดของ MyFirstBot"

#include "includes/Constants.mqh"
#include "includes/OrderManager.mqh"
#include "includes/IndicatorManager.mqh"

//--- Input Parameters
input group "=== Test Settings ==="
input bool EnableBacktest = true;         // เปิดใช้งาน Backtest
input datetime StartDate = D'2024.01.01'; // วันที่เริ่มต้น
input datetime EndDate = D'2024.12.31';   // วันที่สิ้นสุด
input double InitialDeposit = 10000;      // เงินทุนเริ่มต้น

input group "=== Strategy Parameters ==="
input double RiskPercent = 2.0;           // ความเสี่ยงต่อการเทรด
input double StopLossPips = 50;           // Stop Loss (pips)
input double TakeProfitPips = 100;        // Take Profit (pips)

//--- Global Variables
COrderManager orderManager;
CIndicatorManager indicatorManager;

//--- Statistics
int totalTrades = 0;
int winningTrades = 0;
int losingTrades = 0;
double totalProfit = 0;
double maxDrawdown = 0;
double currentBalance = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== Strategy Tester Initialization ===");
    
    // เริ่มต้น Order Manager
    orderManager.SetSymbol(Symbol());
    orderManager.SetStopLoss(StopLossPips * _Point);
    orderManager.SetTakeProfit(TakeProfitPips * _Point);
    
    // คำนวณขนาด Lot
    double lotSize = orderManager.CalculateLotSize(RiskPercent, StopLossPips);
    orderManager.SetLotSize(lotSize);
    
    // เริ่มต้น Indicator Manager
    if(!indicatorManager.Initialize())
    {
        Print("ข้อผิดพลาด: ไม่สามารถเริ่มต้น Indicators ได้");
        return INIT_FAILED;
    }
    
    // ตั้งค่าเงินทุนเริ่มต้น
    currentBalance = InitialDeposit;
    
    Print("=== Strategy Tester พร้อมใช้งาน ===");
    Print("เงินทุนเริ่มต้น: ", InitialDeposit);
    Print("ขนาด Lot: ", lotSize);
    Print("Stop Loss: ", StopLossPips, " pips");
    Print("Take Profit: ", TakeProfitPips, " pips");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== Strategy Tester Results ===");
    Print("จำนวนการเทรดทั้งหมด: ", totalTrades);
    Print("การเทรดที่ชนะ: ", winningTrades);
    Print("การเทรดที่แพ้: ", losingTrades);
    Print("ผลกำไรรวม: ", DoubleToString(totalProfit, 2));
    Print("Drawdown สูงสุด: ", DoubleToString(maxDrawdown, 2));
    Print("เงินทุนสุดท้าย: ", DoubleToString(currentBalance, 2));
    
    if(totalTrades > 0)
    {
        double winRate = (double)winningTrades / totalTrades * 100;
        Print("อัตราการชนะ: ", DoubleToString(winRate, 2), "%");
    }
    
    // ปิด Indicators
    indicatorManager.Deinitialize();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // ตรวจสอบการเปิดใช้งาน Backtest
    if(!EnableBacktest)
        return;
        
    // ตรวจสอบช่วงวันที่
    if(TimeCurrent() < StartDate || TimeCurrent() > EndDate)
        return;
        
    // อัพเดทข้อมูล Indicators
    if(!indicatorManager.UpdateData())
        return;
        
    // ตรวจสอบสัญญาณการเทรด
    CheckTradingSignals();
    
    // อัพเดทสถิติ
    UpdateStatistics();
}

//+------------------------------------------------------------------+
//| ตรวจสอบสัญญาณการเทรด                                            |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    // ตรวจสอบว่ามี Order เปิดอยู่หรือไม่
    if(orderManager.HasOpenOrders())
        return;
        
    // ตรวจสอบสัญญาณ Buy
    if(indicatorManager.CheckBuySignal())
    {
        Print("=== สัญญาณ BUY ===");
        if(orderManager.OpenBuyOrder())
        {
            totalTrades++;
            Print("เปิด Order BUY สำเร็จ - Trade #", totalTrades);
        }
    }
    
    // ตรวจสอบสัญญาณ Sell
    else if(indicatorManager.CheckSellSignal())
    {
        Print("=== สัญญาณ SELL ===");
        if(orderManager.OpenSellOrder())
        {
            totalTrades++;
            Print("เปิด Order SELL สำเร็จ - Trade #", totalTrades);
        }
    }
}

//+------------------------------------------------------------------+
//| อัพเดทสถิติ                                                      |
//+------------------------------------------------------------------+
void UpdateStatistics()
{
    double currentProfit = orderManager.GetCurrentProfit();
    
    // อัพเดทผลกำไร
    if(currentProfit != 0)
    {
        totalProfit += currentProfit;
        currentBalance = InitialDeposit + totalProfit;
        
        // ตรวจสอบ Drawdown
        double drawdown = InitialDeposit - currentBalance;
        if(drawdown > maxDrawdown)
            maxDrawdown = drawdown;
            
        // ตรวจสอบการปิด Order
        if(!orderManager.HasOpenOrders() && currentProfit != 0)
        {
            if(currentProfit > 0)
                winningTrades++;
            else
                losingTrades++;
                
            Print("Order ถูกปิด - ผลกำไร: ", DoubleToString(currentProfit, 2));
        }
    }
}

//+------------------------------------------------------------------+
//| ฟังก์ชันสำหรับการแสดงข้อมูลบนชาร์ต                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        Comment("=== Strategy Tester Status ===\n",
                "สัญลักษณ์: ", Symbol(), "\n",
                "จำนวนการเทรด: ", totalTrades, "\n",
                "การเทรดที่ชนะ: ", winningTrades, "\n",
                "การเทรดที่แพ้: ", losingTrades, "\n",
                "ผลกำไรรวม: ", DoubleToString(totalProfit, 2), "\n",
                "Drawdown: ", DoubleToString(maxDrawdown, 2), "\n",
                "เงินทุนปัจจุบัน: ", DoubleToString(currentBalance, 2), "\n",
                "เวลา: ", TimeToString(TimeCurrent()));
    }
}

//+------------------------------------------------------------------+
//| ฟังก์ชันสำหรับการทดสอบกลยุทธ์                                    |
//+------------------------------------------------------------------+
void TestStrategy()
{
    Print("=== เริ่มการทดสอบกลยุทธ์ ===");
    
    // ทดสอบการคำนวณ Lot Size
    double lotSize = orderManager.CalculateLotSize(RiskPercent, StopLossPips);
    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);
    
    // ทดสอบการตรวจสอบสัญญาณ
    if(indicatorManager.UpdateData())
    {
        Print("RSI Buy Signal: ", indicatorManager.IsRSIBuySignal());
        Print("RSI Sell Signal: ", indicatorManager.IsRSISellSignal());
        Print("MACD Buy Signal: ", indicatorManager.IsMACDBuySignal());
        Print("MACD Sell Signal: ", indicatorManager.IsMACDSellSignal());
        Print("Bollinger Buy Signal: ", indicatorManager.IsBollingerBuySignal());
        Print("Bollinger Sell Signal: ", indicatorManager.IsBollingerSellSignal());
        
        Print("Signal Strength: ", DoubleToString(indicatorManager.GetSignalStrength(), 2), "%");
    }
    
    Print("=== การทดสอบกลยุทธ์เสร็จสิ้น ===");
}
