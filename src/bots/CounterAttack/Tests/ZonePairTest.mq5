//+------------------------------------------------------------------+
//|                                                 ZonePairTest.mq5 |
//|                                  CounterAttack Trading Bot v1.0  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CounterAttack Bot"
#property version   "1.00"
#property description "Test Zone Pairing Functionality"

#include "../includes/Constants.mqh"
#include "../classes/ZoneManager.mqh"

//--- Input Parameters
input string         InpSymbol = "XAUUSD";           // Symbol to test
input double         InpBinSizePoints = 20.0;        // Bin size in points
input int            InpMaxZones = 8;                // Maximum zones
input int            InpMaxZonePairs = 4;            // Maximum zone pairs

//--- Global Variables
CZoneManager*        g_zoneManager = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== Zone Pair Test Initialization ===");
   
   // Initialize Zone Manager
   g_zoneManager = new CZoneManager(InpSymbol);
   if(!g_zoneManager)
   {
      Print("Error: Failed to initialize Zone Manager");
      return INIT_FAILED;
   }
   
   // Configure Zone Manager
   g_zoneManager.SetZoneWidthMultiplier(0.25);
   g_zoneManager.SetTouchTolerance(0.25);
   g_zoneManager.SetMaxZones(InpMaxZones);
   g_zoneManager.SetMaxZonePairs(InpMaxZonePairs);
   
   Print("Zone Manager initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Zone Pair Test shutting down...");
   
   if(g_zoneManager)
      delete g_zoneManager;
   
   Print("Zone Pair Test shutdown complete");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Test zone pairing functionality
   TestZonePairing();
}

//+------------------------------------------------------------------+
//| Test zone pairing functionality                                  |
//+------------------------------------------------------------------+
void TestZonePairing()
{
   if(!g_zoneManager)
      return;
   
   Print("=== Testing Zone Pairing ===");
   
   // Create test zones
   ZoneInfo test_zones[];
   int zone_count = CreateTestZones(test_zones);
   
   if(zone_count < 2)
   {
      Print("Error: Need at least 2 zones to test pairing");
      return;
   }
   
   Print("Created ", zone_count, " test zones");
   
   // Create zones in Zone Manager
   if(!g_zoneManager.CreateZonesFromNodes(-1, test_zones, zone_count, 
                                         test_zones, zone_count, 
                                         test_zones, zone_count, TimeCurrent()))
   {
      Print("Error: Failed to create zones");
      return;
   }
   
   // Test zone pairs
   ZonePair test_pairs[];
   int pair_count;
   if(g_zoneManager.GetFreshZonePairs(test_pairs, pair_count))
   {
      Print("Success! Created ", pair_count, " zone pairs");
      
      for(int i = 0; i < pair_count; i++)
      {
         Print("Pair ", i + 1, ":");
         Print("  Support: ", DoubleToString(test_pairs[i].support_zone.mid_price, 2));
         Print("  Resistance: ", DoubleToString(test_pairs[i].resistance_zone.mid_price, 2));
         Print("  Range Height: ", DoubleToString(test_pairs[i].range_height, 2));
         Print("  Range Ratio: ", DoubleToString(test_pairs[i].range_ratio, 2));
         Print("  Pair Score: ", DoubleToString(test_pairs[i].pair_score, 2));
      }
   }
   else
   {
      Print("No zone pairs created");
   }
}

//+------------------------------------------------------------------+
//| Create test zones for testing                                   |
//+------------------------------------------------------------------+
int CreateTestZones(ZoneInfo &zones[])
{
   ArrayResize(zones, 4);
   
   // Zone 1: HVN (High Volume Node)
   zones[0].mid_price = 2340.0;
   zones[0].upper_price = 2341.0;
   zones[0].lower_price = 2339.0;
   zones[0].type = ZONE_HVN;
   zones[0].status = ZONE_FRESH;
   zones[0].created_at = TimeCurrent();
   zones[0].volume = 1000;
   zones[0].score = 8.5;
   
   // Zone 2: LVN (Low Volume Node)
   zones[1].mid_price = 2345.0;
   zones[1].upper_price = 2346.0;
   zones[1].lower_price = 2344.0;
   zones[1].type = ZONE_LVN;
   zones[1].status = ZONE_FRESH;
   zones[1].created_at = TimeCurrent();
   zones[1].volume = 800;
   zones[1].score = 7.2;
   
   // Zone 3: Another HVN
   zones[2].mid_price = 2335.0;
   zones[2].upper_price = 2336.0;
   zones[2].lower_price = 2334.0;
   zones[2].type = ZONE_HVN;
   zones[2].status = ZONE_FRESH;
   zones[2].created_at = TimeCurrent();
   zones[2].volume = 1200;
   zones[2].score = 9.0;
   
   // Zone 4: Another LVN
   zones[3].mid_price = 2350.0;
   zones[3].upper_price = 2351.0;
   zones[3].lower_price = 2349.0;
   zones[3].type = ZONE_LVN;
   zones[3].status = ZONE_FRESH;
   zones[3].created_at = TimeCurrent();
   zones[3].volume = 600;
   zones[3].score = 6.8;
   
   return 4;
}
