# CounterAttack Trading Bot

## Overview

CounterAttack เป็น Trading Bot ที่ใช้ Volume Profile Analysis เพื่อหา Support/Resistance zones ที่มีประสิทธิภาพ โดยใช้ข้อมูล tick จาก MT5 มาสร้าง Volume Profile และระบุ POC (Point of Control), HVN (High Volume Nodes), และ LVN (Low Volume Nodes) เพื่อสร้างโซนการเทรด

## Key Features

### 🎯 Core Functionality

- **Volume Profile Analysis**: สร้าง Volume Profile จาก tick data แบบ real-time
- **Smart Zone Detection**: ระบุ POC, HVN, LVN zones อัตโนมัติ
- **Freshness Tracking**: ติดตามสถานะ "สด" ของโซน (ยังไม่ถูกแตะ)
- **Multi-Filter System**: ระบบกรองโซนด้วย Volatility, Trend, และ Price Action
- **Intelligent Scoring**: ให้คะแนนโซนตามความสำคัญและความใกล้เคียงกับราคา

### 🔧 Modular Architecture

- **TickDataLoader**: จัดการการดาวน์โหลด tick data แบบ chunk
- **VolumeProfileCalculator**: คำนวณ Volume Profile และหา nodes
- **ZoneManager**: สร้างและจัดการโซนราคา + ตรวจสอบ freshness
- **FilterSystem**: ระบบกรองโซนขั้นสูงด้วย Advanced Volatility, Trend, Price Action
- **ScoringSystem**: ให้คะแนนและจัดอันดับโซน
- **OutputSystem**: ส่งออกข้อมูลเป็นไฟล์และวาดบนกราฟ

## Installation

1. Copy ทั้งโฟลเดอร์ `CounterAttack` ไปยัง `MQL5/Experts/`
2. Compile ไฟล์ `CounterAttack.mq5` ใน MetaEditor
3. Attach EA ลงบนกราฟ XAUUSD H1 หรือ H4

## Configuration

### Range Settings

- **RangeMode**: เลือกระหว่าง SESSION (ตามเซสชัน) หรือ FIXED_RANGE (จำนวนแท่งคงที่)
- **Session Times**: กำหนดเวลาเซสชันสำหรับการคำนวณ (เช่น Daily: 00:00-23:59)
- **BarsBack**: จำนวนแท่งย้อนหลังสำหรับ FIXED_RANGE mode

### Volume Profile Settings

- **BinSizePoints**: ขนาด bin ในหน่วย points (แนะนำ 10-20 สำหรับ XAUUSD)
- **SmoothWindow**: หน้าต่างการปรับให้เรียบ (แนะนำ 3)
- **HVN/LVN Percentile**: เกณฑ์การระบุ HVN (80%) และ LVN (25%)

### Zone Settings

- **ZoneWidthMultiplier**: ความกว้างโซนเป็นเท่าของ ATR (แนะนำ 0.25)
- **TouchTolerance**: ความอดทนในการตรวจจับการแตะโซน (แนะนำ 0.25)
- **MaxZones**: จำนวนโซนสูงสุดที่จะแสดง (แนะนำ 8-12)

### Advanced Filter Settings

- **Enhanced Volatility Filter**:
  - ATR ≥ ค่ามัธยฐานย้อนหลัง (ตลาดมีแรง)
  - ไม่อยู่ใน 20% ล่างของ ATR (หลีกเลี่ยงตลาดนิ่ง)
- **Advanced Trend Filter**:
  - วิเคราะห์ทิศทางด้วย Fast/Slow EMA
  - ตรวจสอบความแรงของเทรนด์
  - โซนต้านใช้ในดาวน์เทรนด์ โซนรับใช้ในอัพเทรนด์
- **Enhanced Price Action Filter**:
  - Pin Bar rejection patterns
  - Engulfing patterns
  - Long wick rejections
  - Volume spike confirmations (120-150% ของค่าเฉลี่ย)

## Usage

### Enhanced Business Process Flow

**Tick → Bin → HVN/LVN → Zone(ATR) → Freshness → Advanced Filters → Score → Output**

1. **Tick Data Processing**: ดาวน์โหลด tick data แบบ chunk จาก MT5
2. **Volume Profile Creation**: สร้าง bins และคำนวณ volume distribution
3. **Node Detection**: หา POC, HVN, LVN จาก percentile analysis
4. **Zone Creation**: สร้างโซนด้วย ATR-based width
5. **Freshness Tracking**: ติดตามสถานะ "Fresh" (ยังไม่ถูกแตะ)
6. **Advanced Filtering**:
   - Volatility Filter: ตรวจสอบ ATR strength
   - Trend Filter: วิเคราะห์ trend alignment
   - Price Action Filter: หา rejection patterns
7. **Intelligent Scoring**: ให้คะแนนตาม prominence + proximity + type + freshness
8. **Output**: ส่งออกเฉพาะโซนที่มี "market context support"

### Output Files

- **fresh_zones_SYMBOL_TIMEFRAME.csv**: ข้อมูลโซนในรูปแบบ CSV
- รวมข้อมูล: timestamp, type, mid_price, upper_price, lower_price, status, score

### Chart Display

- **Yellow Zones**: POC (Point of Control)
- **Light Blue Zones**: HVN (High Volume Nodes)
- **Light Pink Zones**: LVN (Low Volume Nodes)
- **Labels**: แสดงประเภทโซนและคะแนน

## Recommended Settings for XAUUSD

### H1 Timeframe

```
RangeMode = SESSION
SessionStart = 00:00
SessionEnd = 23:59
BinSizePoints = 15
ZoneWidthMultiplier = 0.25
TouchTolerance = 0.25
MaxZones = 10
```

### H4 Timeframe

```
RangeMode = SESSION
SessionStart = 00:00
SessionEnd = 23:59
BinSizePoints = 20
ZoneWidthMultiplier = 0.3
TouchTolerance = 0.3
MaxZones = 8
```

## Understanding Zone Types

### POC (Point of Control)

- ระดับราคาที่มี volume สูงสุด
- มักเป็นจุดสมดุลของตลาด
- ให้ความสำคัญสูงสุด

### HVN (High Volume Nodes)

- ระดับราคาที่มี volume สูง (> 80th percentile)
- มักทำหน้าที่เป็น support/resistance
- เหมาะสำหรับการ reversal trading

### LVN (Low Volume Nodes)

- ระดับราคาที่มี volume ต่ำ (< 25th percentile)
- มักเป็นจุดที่ราคาผ่านไปอย่างรวดเร็ว
- เหมาะสำหรับการ breakout trading

## Troubleshooting

### Common Issues

1. **"Insufficient tick data"**: ตรวจสอบ History Center ใน MT5
2. **"No zones found"**: ลองปรับ percentile settings
3. **"Zones not updating"**: ตรวจสอบ timer interval และ session times

### Performance Tips

- ใช้ H1 หรือ H4 timeframe สำหรับประสิทธิภาพที่ดี
- ปรับ MinTicks ให้เหมาะสมกับข้อมูลที่มี
- ใช้ SmoothWindow = 3 เพื่อลด noise

## File Structure

```
CounterAttack/
├── CounterAttack.mq5          # Main EA file
├── README.md                  # This file
├── includes/
│   └── Constants.mqh          # Constants and structures
└── classes/
    ├── TickDataLoader.mqh     # Tick data management
    ├── VolumeProfileCalculator.mqh  # Volume profile calculation
    ├── ZoneManager.mqh        # Zone creation and freshness tracking
    ├── FilterSystem.mqh       # Multi-filter system
    └── ScoringSystem.mqh      # Scoring and output system
```

## Version History

- **v1.0**: Initial release with full Volume Profile functionality

## Support

สำหรับการสนับสนุนและคำถาม กรุณาตรวจสอบ logs ใน MT5 Expert tab และ Journal tab

---

**Note**: EA นี้ออกแบบมาสำหรับ iUX XAUUSD โดยเฉพาะ แต่สามารถปรับใช้กับสัญลักษณ์อื่นได้โดยการปรับพารามิเตอร์ให้เหมาะสม
