# คู่มือการใช้งาน MyFirstBot

## ภาพรวม

MyFirstBot เป็น EA (Expert Advisor) สำหรับ MetaTrader 5 ที่ออกแบบมาเพื่อเทรดทองคำ (XAUUSD) โดยใช้กลยุทธ์การรอสัญญาณจาก 3 indicators พร้อมกัน

## กลยุทธ์การเทรด

### หลักการทำงาน

EA จะรอสัญญาณจาก 3 indicators พร้อมกันก่อนเปิด Order:

1. **RSI (Relative Strength Index)**

   - สัญญาณ Buy: RSI ขึ้นจาก oversold (30) หรือกำลังเพิ่มขึ้น
   - สัญญาณ Sell: RSI ลงจาก overbought (70) หรือกำลังลดลง

2. **MACD (Moving Average Convergence Divergence)**

   - สัญญาณ Buy: MACD ขึ้นเหนือ Signal และกำลังเพิ่มขึ้น
   - สัญญาณ Sell: MACD ลงใต้ Signal และกำลังลดลง

3. **Bollinger Bands**
   - สัญญาณ Buy: ราคาปิดใกล้หรือต่ำกว่า Lower Band (oversold)
   - สัญญาณ Sell: ราคาปิดใกล้หรือสูงกว่า Upper Band (overbought)

### การเปิด Order

- **BUY**: เมื่อทั้ง 3 indicators ให้สัญญาณ Buy พร้อมกัน
- **SELL**: เมื่อทั้ง 3 indicators ให้สัญญาณ Sell พร้อมกัน
- **Risk Management**: ใช้ Stop Loss และ Take Profit ที่กำหนด

## การติดตั้ง

### ขั้นตอนที่ 1: คัดลอกไฟล์

1. คัดลอกไฟล์ `src/main.mq5` ไปยังโฟลเดอร์ `MQL5/Experts/` ของ MT5
2. รีสตาร์ท MetaTrader 5

### ขั้นตอนที่ 2: เปิดชาร์ต

1. เปิดชาร์ต XAUUSD ใน MT5
2. เลือก Timeframe ที่ต้องการ (แนะนำ M15 หรือ H1)

### ขั้นตอนที่ 3: ตั้งค่า EA

1. ลาก EA จาก Navigator ไปยังชาร์ต
2. ตั้งค่าพารามิเตอร์ตามต้องการ
3. กดปุ่ม "OK"

## การตั้งค่าพารามิเตอร์

### Risk Management

- **RiskPercent**: ความเสี่ยงต่อการเทรด (เปอร์เซ็นต์) - ค่าเริ่มต้น: 2.0%
- **StopLossPips**: Stop Loss ในหน่วย pips - ค่าเริ่มต้น: 50
- **TakeProfitPips**: Take Profit ในหน่วย pips - ค่าเริ่มต้น: 100

### Indicator Settings

- **RSIPeriod**: คาบเวลาของ RSI - ค่าเริ่มต้น: 14
- **MACDFastEMA**: Fast EMA สำหรับ MACD - ค่าเริ่มต้น: 12
- **MACDSlowEMA**: Slow EMA สำหรับ MACD - ค่าเริ่มต้น: 26
- **MACDSignalEMA**: Signal EMA สำหรับ MACD - ค่าเริ่มต้น: 9
- **BollingerPeriod**: คาบเวลาของ Bollinger Bands - ค่าเริ่มต้น: 20
- **BollingerDeviation**: ค่า Standard Deviation - ค่าเริ่มต้น: 2.0

### Trading Settings

- **EnableTrading**: เปิด/ปิดการเทรด - ค่าเริ่มต้น: true
- **MagicNumber**: Magic Number สำหรับระบุ Order - ค่าเริ่มต้น: 123456
- **TradeComment**: ข้อความใน Order - ค่าเริ่มต้น: "MyFirstBot"

## การใช้งาน

### การเริ่มต้น

1. เปิดใช้งาน AutoTrading ใน MT5
2. EA จะเริ่มทำงานทันที
3. ตรวจสอบ Journal และ Expert tabs สำหรับข้อมูลการทำงาน

### การหยุดการทำงาน

1. ปิด EA จากชาร์ต
2. หรือตั้งค่า EnableTrading = false

### การตรวจสอบสถานะ

- ดูข้อมูลใน Comment บนชาร์ต
- ตรวจสอบ Journal tab
- ดูผลกำไรใน Account tab

## การทดสอบ

### Backtest

1. เปิด Strategy Tester ใน MT5
2. เลือก EA: MyFirstBot
3. เลือกสัญลักษณ์: XAUUSD
4. เลือก Timeframe: M15 หรือ H1
5. ตั้งค่าวันที่เริ่มต้นและสิ้นสุด
6. กดปุ่ม "Start"

### Forward Test

1. ใช้กับ Account Demo ก่อน
2. ตรวจสอบการทำงานในสภาพแวดล้อมจริง
3. ปรับแต่งพารามิเตอร์ตามผลลัพธ์

## การปรับแต่ง

### การปรับ Risk Management

- ลด RiskPercent หากต้องการความเสี่ยงต่ำ
- เพิ่ม StopLossPips หากต้องการให้ Order อยู่ได้นานขึ้น
- ปรับ TakeProfitPips ตามเป้าหมายกำไร

### การปรับ Indicators

- ปรับคาบเวลาของ RSI ตามความต้องการ
- เปลี่ยนค่า EMA ของ MACD ตามความเร็วของตลาด
- ปรับ Bollinger Deviation ตามความผันผวน

## ข้อควรระวัง

### ความเสี่ยง

- ใช้กับ Account Demo ก่อน
- เริ่มต้นด้วยเงินทุนน้อย
- ตรวจสอบการตั้งค่า Risk Management

### การบำรุงรักษา

- ตรวจสอบการทำงานของ EA เป็นประจำ
- อัพเดทพารามิเตอร์ตามสภาพตลาด
- สำรองข้อมูลการตั้งค่า

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **EA ไม่ทำงาน**: ตรวจสอบ AutoTrading และการตั้งค่า
2. **ไม่เปิด Order**: ตรวจสอบสัญญาณจาก Indicators
3. **Order ถูกปิดทันที**: ตรวจสอบ Stop Loss และ Take Profit

### การติดต่อ

หากมีปัญหาหรือคำถาม กรุณาตรวจสอบ:

- Journal tab สำหรับข้อผิดพลาด
- Expert tab สำหรับข้อมูลการทำงาน
- คู่มือนี้สำหรับคำแนะนำ

## หมายเหตุสำคัญ

- EA นี้ถูกออกแบบมาสำหรับการศึกษาและทดสอบ
- ใช้ด้วยความระมัดระวังและความรับผิดชอบ
- ผลการเทรดในอดีตไม่รับประกันผลลัพธ์ในอนาคต
- ตรวจสอบกฎหมายและข้อบังคับของโบรกเกอร์ก่อนใช้งาน
