# ตัวอย่างการใช้งาน MyFirstBot

## ตัวอย่างที่ 1: การใช้งานพื้นฐาน

### การเริ่มต้น EA

```mql5
// ตั้งค่าพารามิเตอร์พื้นฐาน
input double RiskPercent = 2.0;           // ความเสี่ยง 2%
input double StopLossPips = 50;           // Stop Loss 50 pips
input double TakeProfitPips = 100;        // Take Profit 100 pips

// เริ่มต้น EA
void OnInit()
{
    Print("=== MyFirstBot เริ่มต้น ===");

    // ตรวจสอบสัญลักษณ์
    if(Symbol() != "XAUUSD")
    {
        Print("คำเตือน: ใช้กับ XAUUSD เท่านั้น");
        return;
    }

    Print("EA พร้อมใช้งาน");
}
```

### การตรวจสอบสัญญาณ

```mql5
void OnTick()
{
    // ตรวจสอบแท่งเทียนใหม่
    if(!IsNewBar())
        return;

    // อัพเดทข้อมูล Indicators
    if(!indicatorManager.UpdateData())
        return;

    // ตรวจสอบสัญญาณ Buy
    if(indicatorManager.CheckBuySignal())
    {
        Print("=== สัญญาณ BUY ===");
        // เปิด Order Buy
        if(orderManager.OpenBuyOrder())
            Print("เปิด Order BUY สำเร็จ");
    }

    // ตรวจสอบสัญญาณ Sell
    else if(indicatorManager.CheckSellSignal())
    {
        Print("=== สัญญาณ SELL ===");
        // เปิด Order Sell
        if(orderManager.OpenSellOrder())
            Print("เปิด Order SELL สำเร็จ");
    }
}
```

## ตัวอย่างที่ 2: การปรับแต่ง Risk Management

### การคำนวณ Lot Size แบบปรับได้

```mql5
// ฟังก์ชันคำนวณ Lot Size ตามความเสี่ยง
double CalculateDynamicLotSize(double riskPercent, double stopLossPips)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * riskPercent / 100;

    // คำนวณตามสัญลักษณ์
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    if(tickSize > 0 && stopLossPips > 0)
    {
        double lotSize = riskAmount / (stopLossPips * tickValue / tickSize);
        lotSize = MathMin(lotSize, MAX_LOT);
        lotSize = MathMax(lotSize, MIN_LOT);
        return NormalizeDouble(lotSize, 2);
    }

    return MIN_LOT;
}

// การใช้งาน
void SetRiskManagement()
{
    double lotSize = CalculateDynamicLotSize(RiskPercent, StopLossPips);
    orderManager.SetLotSize(lotSize);

    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);
    Print("ความเสี่ยง: ", RiskPercent, "%");
    Print("Stop Loss: ", StopLossPips, " pips");
}
```

### การปรับ Stop Loss แบบ Dynamic

```mql5
// ฟังก์ชันปรับ Stop Loss แบบ Dynamic
void UpdateDynamicStopLoss()
{
    if(!orderManager.HasOpenOrders())
        return;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            double currentSL = PositionGetDouble(POSITION_SL);
            double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
                // สำหรับ Buy Order - ย้าย Stop Loss ขึ้น
                double newSL = currentPrice - (StopLossPips * _Point);
                if(newSL > currentSL && newSL < currentPrice)
                {
                    // อัพเดท Stop Loss
                    trade.PositionModify(PositionGetTicket(i), newSL,
                                       PositionGetDouble(POSITION_TP));
                }
            }
            else
            {
                // สำหรับ Sell Order - ย้าย Stop Loss ลง
                double newSL = currentPrice + (StopLossPips * _Point);
                if(newSL < currentSL && newSL > currentPrice)
                {
                    // อัพเดท Stop Loss
                    trade.PositionModify(PositionGetTicket(i), newSL,
                                       PositionGetDouble(POSITION_TP));
                }
            }
        }
    }
}
```

## ตัวอย่างที่ 3: การปรับแต่ง Indicators

### การปรับค่า RSI แบบ Dynamic

```mql5
// ฟังก์ชันปรับค่า RSI ตามความผันผวนของตลาด
void AdjustRSIParameters()
{
    double atr = iATR(Symbol(), Period(), 14, 0);
    double atrPercent = (atr / iClose(Symbol(), Period(), 0)) * 100;

    // ปรับค่า RSI ตามความผันผวน
    if(atrPercent > 2.0) // ความผันผวนสูง
    {
        RSI_OVERBOUGHT = 75;  // เพิ่มค่า overbought
        RSI_OVERSOLD = 25;    // ลดค่า oversold
    }
    else if(atrPercent < 1.0) // ความผันผวนต่ำ
    {
        RSI_OVERBOUGHT = 65;  // ลดค่า overbought
        RSI_OVERSOLD = 35;    // เพิ่มค่า oversold
    }
    else // ความผันผวนปกติ
    {
        RSI_OVERBOUGHT = 70;  // ค่าเริ่มต้น
        RSI_OVERSOLD = 30;    // ค่าเริ่มต้น
    }

    Print("ปรับค่า RSI - Overbought: ", RSI_OVERBOUGHT, ", Oversold: ", RSI_OVERSOLD);
}
```

### การปรับค่า MACD แบบ Adaptive

```mql5
// ฟังก์ชันปรับค่า MACD ตาม Timeframe
void AdjustMACDParameters()
{
    ENUM_TIMEFRAMES currentPeriod = Period();

    switch(currentPeriod)
    {
        case PERIOD_M1:
        case PERIOD_M5:
            MACD_FAST_EMA = 8;   // เร็วขึ้นสำหรับ Timeframe ต่ำ
            MACD_SLOW_EMA = 21;
            MACD_SIGNAL_EMA = 7;
            break;

        case PERIOD_M15:
        case PERIOD_M30:
            MACD_FAST_EMA = 12;  // ค่าเริ่มต้น
            MACD_SLOW_EMA = 26;
            MACD_SIGNAL_EMA = 9;
            break;

        case PERIOD_H1:
        case PERIOD_H4:
            MACD_FAST_EMA = 16;  // ช้าลงสำหรับ Timeframe สูง
            MACD_SLOW_EMA = 32;
            MACD_SIGNAL_EMA = 11;
            break;

        case PERIOD_D1:
            MACD_FAST_EMA = 21;  // ช้ามากสำหรับ Daily
            MACD_SLOW_EMA = 55;
            MACD_SIGNAL_EMA = 13;
            break;
    }

    Print("ปรับค่า MACD - Fast: ", MACD_FAST_EMA, ", Slow: ", MACD_SLOW_EMA,
          ", Signal: ", MACD_SIGNAL_EMA);
}
```

## ตัวอย่างที่ 4: การจัดการเวลาและเงื่อนไข

### การตรวจสอบเวลาการเทรดแบบละเอียด

```mql5
// ฟังก์ชันตรวจสอบเวลาการเทรดแบบละเอียด
bool IsOptimalTradingTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // ตรวจสอบวันทำงาน
    if(dt.day_of_week == 0 || dt.day_of_week == 6)
        return false;

    // ตรวจสอบช่วงเวลาที่เหมาะสม
    int currentHour = dt.hour;

    // ช่วงเวลาที่ดีที่สุดสำหรับ XAUUSD
    bool isLondonOpen = (currentHour >= 8 && currentHour < 16);   // London Session
    bool isNewYorkOpen = (currentHour >= 13 && currentHour < 21); // New York Session
    bool isAsianOpen = (currentHour >= 0 && currentHour < 8);     // Asian Session

    // หลีกเลี่ยงช่วงเวลาที่ตลาดปิด
    bool isMarketClosed = (currentHour >= 22 || currentHour < 2);

    return (isLondonOpen || isNewYorkOpen || isAsianOpen) && !isMarketClosed;
}

// การใช้งาน
void CheckTradingConditions()
{
    if(!IsOptimalTradingTime())
    {
        Print("ไม่ใช่เวลาการเทรดที่เหมาะสม");
        return;
    }

    // ตรวจสอบเงื่อนไขอื่นๆ
    if(!CheckSpread())
        return;

    if(!CheckVolatility())
        return;

    // ดำเนินการเทรด
    CheckTradingSignals();
}
```

### การตรวจสอบความผันผวนของตลาด

```mql5
// ฟังก์ชันตรวจสอบความผันผวน
bool CheckVolatility()
{
    double atr = iATR(Symbol(), Period(), 14, 0);
    double atrPercent = (atr / iClose(Symbol(), Period(), 0)) * 100;

    // ความผันผวนต่ำเกินไป - ไม่เหมาะสำหรับการเทรด
    if(atrPercent < 0.5)
    {
        Print("ความผันผวนต่ำเกินไป: ", DoubleToString(atrPercent, 2), "%");
        return false;
    }

    // ความผันผวนสูงเกินไป - เสี่ยงเกินไป
    if(atrPercent > 5.0)
    {
        Print("ความผันผวนสูงเกินไป: ", DoubleToString(atrPercent, 2), "%");
        return false;
    }

    return true;
}
```

## ตัวอย่างที่ 5: การจัดการข้อผิดพลาด

### การจัดการข้อผิดพลาดแบบครอบคลุม

```mql5
// ฟังก์ชันจัดการข้อผิดพลาด
void HandleError(int errorCode, string operation)
{
    string errorDescription = "";

    switch(errorCode)
    {
        case ERR_NO_ERROR:
            return;

        case ERR_TRADE_DISABLED:
            errorDescription = "การเทรดถูกปิดใช้งาน";
            break;

        case ERR_MARKET_CLOSED:
            errorDescription = "ตลาดปิด";
            break;

        case ERR_INVALID_STOPS:
            errorDescription = "Stop Loss หรือ Take Profit ไม่ถูกต้อง";
            break;

        case ERR_INVALID_TRADE_PARAMETERS:
            errorDescription = "พารามิเตอร์การเทรดไม่ถูกต้อง";
            break;

        case ERR_SERVER_BUSY:
            errorDescription = "เซิร์ฟเวอร์ไม่ว่าง";
            break;

        case ERR_NO_CONNECTION:
            errorDescription = "ไม่มีการเชื่อมต่อ";
            break;

        default:
            errorDescription = "ข้อผิดพลาดที่ไม่ทราบ: " + IntegerToString(errorCode);
            break;
    }

    Print("ข้อผิดพลาดในการ ", operation, ": ", errorDescription);

    // บันทึกข้อผิดพลาด
    LogError(errorCode, operation, errorDescription);
}

// ฟังก์ชันบันทึกข้อผิดพลาด
void LogError(int errorCode, string operation, string description)
{
    string logMessage = StringFormat("%s | %s | %s | %s",
                                   TimeToString(TimeCurrent()),
                                   operation,
                                   IntegerToString(errorCode),
                                   description);

    // บันทึกลงไฟล์
    int fileHandle = FileOpen("error_log.txt", FILE_WRITE | FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        FileSeek(fileHandle, 0, SEEK_END);
        FileWriteString(fileHandle, logMessage + "\n");
        FileClose(fileHandle);
    }
}
```

## ตัวอย่างที่ 6: การทดสอบกลยุทธ์

### การทดสอบกลยุทธ์แบบง่าย

```mql5
// ฟังก์ชันทดสอบกลยุทธ์
void TestStrategy()
{
    Print("=== เริ่มการทดสอบกลยุทธ์ ===");

    // ทดสอบการคำนวณ Lot Size
    double lotSize = orderManager.CalculateLotSize(RiskPercent, StopLossPips);
    Print("ขนาด Lot ที่คำนวณได้: ", lotSize);

    // ทดสอบการตรวจสอบสัญญาณ
    if(indicatorManager.UpdateData())
    {
        Print("RSI Buy Signal: ", indicatorManager.IsRSIBuySignal());
        Print("RSI Sell Signal: ", indicatorManager.IsRSISellSignal());
        Print("MACD Buy Signal: ", indicatorManager.IsMACDBuySignal());
        Print("MACD Sell Signal: ", indicatorManager.IsMACDSellSignal());
        Print("Bollinger Buy Signal: ", indicatorManager.IsBollingerBuySignal());
        Print("Bollinger Sell Signal: ", indicatorManager.IsBollingerSellSignal());

        Print("Signal Strength: ", DoubleToString(indicatorManager.GetSignalStrength(), 2), "%");
    }

    // ทดสอบการคำนวณ Risk
    double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100;
    Print("จำนวนเงินที่เสี่ยง: ", DoubleToString(riskAmount, 2));

    Print("=== การทดสอบกลยุทธ์เสร็จสิ้น ===");
}
```

## หมายเหตุสำคัญ

- ตัวอย่างทั้งหมดนี้เป็นเพียงแนวทางในการใช้งาน
- ควรปรับแต่งตามความต้องการและความเสี่ยงของตนเอง
- ทดสอบใน Account Demo ก่อนใช้งานจริง
- ตรวจสอบการทำงานของ EA เป็นประจำ
